"""
MongoDB Service for Dropshipping Dashboard
Handles all database operations and connections
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional, Any
from bson import ObjectId
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, DuplicateKeyError
from motor.motor_asyncio import AsyncIOMotorClient

from config import Config

logger = logging.getLogger(__name__)

class MongoDBService:
    """MongoDB service for handling database operations"""
    
    def __init__(self, config: Config):
        self.config = config
        self.client = None
        self.db = None
        self.async_client = None
        self.async_db = None
        
    def connect(self):
        """Establish synchronous MongoDB connection"""
        try:
            self.client = MongoClient(
                self.config.MONGODB_URI,
                serverSelectionTimeoutMS=5000
            )
            # Test connection
            self.client.admin.command('ping')
            self.db = self.client[self.config.MONGODB_DB_NAME]
            logger.info("Successfully connected to MongoDB")
            self._create_indexes()
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
    
    async def connect_async(self):
        """Establish asynchronous MongoDB connection"""
        try:
            self.async_client = AsyncIOMotorClient(
                self.config.MONGODB_URI,
                serverSelectionTimeoutMS=5000
            )
            # Test connection
            await self.async_client.admin.command('ping')
            self.async_db = self.async_client[self.config.MONGODB_DB_NAME]
            logger.info("Successfully connected to MongoDB (async)")
            await self._create_indexes_async()
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB (async): {e}")
            return False
    
    def _create_indexes(self):
        """Create database indexes for better performance"""
        try:
            # Products collection indexes
            self.db.products.create_index("product_id", unique=True)
            self.db.products.create_index("status")
            self.db.products.create_index("created_at")
            self.db.products.create_index("order_info.order_date")  # For sorting by order date
            self.db.products.create_index([("title", "text"), ("original_content.title", "text")])
            
            # Orders collection indexes
            self.db.orders.create_index("order_id", unique=True)
            self.db.orders.create_index("status")
            self.db.orders.create_index("order_date")
            self.db.orders.create_index("import_batch_id")
            
            # Import batches collection indexes
            self.db.import_batches.create_index("batch_name")
            self.db.import_batches.create_index("import_date")
            self.db.import_batches.create_index("status")
            
            # Pricing rules collection indexes
            self.db.pricing_rules.create_index("rule_name")
            self.db.pricing_rules.create_index("is_active")
            
            # AI configuration collection indexes
            self.db.ai_config.create_index("service_type")
            self.db.ai_config.create_index("provider")
            
            logger.info("Database indexes created successfully")
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
    
    async def _create_indexes_async(self):
        """Create database indexes asynchronously"""
        try:
            # Products collection indexes
            await self.async_db.products.create_index("product_id", unique=True)
            await self.async_db.products.create_index("status")
            await self.async_db.products.create_index("created_at")
            await self.async_db.products.create_index([("title", "text"), ("original_content.title", "text")])
            
            # Orders collection indexes
            await self.async_db.orders.create_index("order_id", unique=True)
            await self.async_db.orders.create_index("status")
            await self.async_db.orders.create_index("order_date")
            await self.async_db.orders.create_index("import_batch_id")
            
            # Import batches collection indexes
            await self.async_db.import_batches.create_index("batch_name")
            await self.async_db.import_batches.create_index("import_date")
            await self.async_db.import_batches.create_index("status")
            
            # Pricing rules collection indexes
            await self.async_db.pricing_rules.create_index("rule_name")
            await self.async_db.pricing_rules.create_index("is_active")
            
            # AI configuration collection indexes
            await self.async_db.ai_config.create_index("service_type")
            await self.async_db.ai_config.create_index("provider")
            
            logger.info("Database indexes created successfully (async)")
        except Exception as e:
            logger.error(f"Error creating indexes (async): {e}")
    
    def close(self):
        """Close database connections"""
        if self.client:
            self.client.close()
        if self.async_client:
            self.async_client.close()
    
    # Product Operations
    def create_product(self, product_data: Dict) -> Optional[str]:
        """Create a new product"""
        try:
            product_data['created_at'] = datetime.now(timezone.utc)
            product_data['updated_at'] = datetime.now(timezone.utc)
            result = self.db.products.insert_one(product_data)
            logger.info(f"Created product with ID: {result.inserted_id}")
            return str(result.inserted_id)
        except DuplicateKeyError:
            logger.error(f"Product with ID {product_data.get('product_id')} already exists")
            return None
        except Exception as e:
            logger.error(f"Error creating product: {e}")
            return None
    
    def get_product(self, product_id: str) -> Optional[Dict]:
        """Get a product by ID"""
        try:
            if ObjectId.is_valid(product_id):
                product = self.db.products.find_one({"_id": ObjectId(product_id)})
            else:
                product = self.db.products.find_one({"product_id": product_id})
            
            if product:
                product['_id'] = str(product['_id'])
            return product
        except Exception as e:
            logger.error(f"Error getting product: {e}")
            return None
    
    def get_products(self, skip: int = 0, limit: int = 50, filters: Dict = None) -> List[Dict]:
        """Get products with pagination and filtering"""
        try:
            query = filters or {}
            # Sort by created_at in ascending order (first orders first, most recent last)
            cursor = self.db.products.find(query).skip(skip).limit(limit).sort("created_at", 1)
            products = []
            for product in cursor:
                product['_id'] = str(product['_id'])
                products.append(product)
            return products
        except Exception as e:
            logger.error(f"Error getting products: {e}")
            return []
    
    def update_product(self, product_id: str, update_data: Dict) -> bool:
        """Update a product"""
        try:
            update_data['updated_at'] = datetime.now(timezone.utc)
            if ObjectId.is_valid(product_id):
                result = self.db.products.update_one(
                    {"_id": ObjectId(product_id)},
                    {"$set": update_data}
                )
            else:
                result = self.db.products.update_one(
                    {"product_id": product_id},
                    {"$set": update_data}
                )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating product: {e}")
            return False
    
    def delete_product(self, product_id: str) -> bool:
        """Delete a product"""
        try:
            if ObjectId.is_valid(product_id):
                result = self.db.products.delete_one({"_id": ObjectId(product_id)})
            else:
                result = self.db.products.delete_one({"product_id": product_id})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting product: {e}")
            return False
    
    # Order Operations
    def create_order(self, order_data: Dict) -> Optional[str]:
        """Create a new order"""
        try:
            order_data['created_at'] = datetime.now(timezone.utc)
            result = self.db.orders.insert_one(order_data)
            logger.info(f"Created order with ID: {result.inserted_id}")
            return str(result.inserted_id)
        except DuplicateKeyError:
            logger.error(f"Order with ID {order_data.get('order_id')} already exists")
            return None
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            return None
    
    def get_orders(self, skip: int = 0, limit: int = 50, filters: Dict = None) -> List[Dict]:
        """Get orders with pagination and filtering"""
        try:
            query = filters or {}
            cursor = self.db.orders.find(query).skip(skip).limit(limit).sort("order_date", -1)
            orders = []
            for order in cursor:
                order['_id'] = str(order['_id'])
                orders.append(order)
            return orders
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    # Import Batch Operations
    def create_import_batch(self, batch_data: Dict) -> Optional[str]:
        """Create a new import batch"""
        try:
            batch_data['import_date'] = datetime.now(timezone.utc)
            batch_data['status'] = 'pending'
            result = self.db.import_batches.insert_one(batch_data)
            logger.info(f"Created import batch with ID: {result.inserted_id}")
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error creating import batch: {e}")
            return None
    
    def update_import_batch(self, batch_id: str, update_data: Dict) -> bool:
        """Update an import batch"""
        try:
            result = self.db.import_batches.update_one(
                {"_id": ObjectId(batch_id)},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating import batch: {e}")
            return False
    
    # Pricing Rules Operations
    def create_pricing_rule(self, rule_data: Dict) -> Optional[str]:
        """Create a new pricing rule"""
        try:
            rule_data['created_at'] = datetime.now(timezone.utc)
            result = self.db.pricing_rules.insert_one(rule_data)
            logger.info(f"Created pricing rule with ID: {result.inserted_id}")
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error creating pricing rule: {e}")
            return None
    
    def get_pricing_rules(self, active_only: bool = True) -> List[Dict]:
        """Get pricing rules"""
        try:
            query = {"is_active": True} if active_only else {}
            cursor = self.db.pricing_rules.find(query).sort("created_at", -1)
            rules = []
            for rule in cursor:
                rule['_id'] = str(rule['_id'])
                rules.append(rule)
            return rules
        except Exception as e:
            logger.error(f"Error getting pricing rules: {e}")
            return []
    
    # AI Configuration Operations
    def get_ai_config(self, service_type: str, provider: str) -> Optional[Dict]:
        """Get AI service configuration"""
        try:
            config = self.db.ai_config.find_one({
                "service_type": service_type,
                "provider": provider
            })
            if config:
                config['_id'] = str(config['_id'])
            return config
        except Exception as e:
            logger.error(f"Error getting AI config: {e}")
            return None
    
    def update_ai_config(self, service_type: str, provider: str, config_data: Dict) -> bool:
        """Update AI service configuration"""
        try:
            result = self.db.ai_config.update_one(
                {"service_type": service_type, "provider": provider},
                {"$set": config_data},
                upsert=True
            )
            return result.modified_count > 0 or result.upserted_id is not None
        except Exception as e:
            logger.error(f"Error updating AI config: {e}")
            return False
    
    # Statistics and Analytics
    def get_dashboard_stats(self) -> Dict:
        """Get dashboard statistics"""
        try:
            stats = {
                'total_products': self.db.products.count_documents({}),
                'ready_products': self.db.products.count_documents({"status": "ready"}),
                'processing_products': self.db.products.count_documents({"status": "processing"}),
                'total_orders': self.db.orders.count_documents({}),
                'completed_orders': self.db.orders.count_documents({"status": "Completed"}),
                'total_import_batches': self.db.import_batches.count_documents({}),
                'active_pricing_rules': self.db.pricing_rules.count_documents({"is_active": True})
            }
            return stats
        except Exception as e:
            logger.error(f"Error getting dashboard stats: {e}")
            return {}
    
    def search_products(self, search_term: str, limit: int = 50) -> List[Dict]:
        """Search products by text"""
        try:
            cursor = self.db.products.find(
                {"$text": {"$search": search_term}}
            ).limit(limit)
            products = []
            for product in cursor:
                product['_id'] = str(product['_id'])
                products.append(product)
            return products
        except Exception as e:
            logger.error(f"Error searching products: {e}")
            return []

# Global MongoDB service instance
mongodb_service = MongoDBService(Config)