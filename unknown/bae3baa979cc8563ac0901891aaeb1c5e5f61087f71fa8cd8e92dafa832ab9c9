# Flask Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True

# MongoDB Configuration
MONGODB_URI=mongodb+srv://wazobianigeri:<EMAIL>/?retryWrites=true&w=majority
MONGODB_DB_NAME=dropshipping_dashboard

# File Upload Settings
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=104857600
IMAGE_STORAGE_PATH=static/images
PROCESSED_IMAGE_PATH=static/processed_images

# AI Service API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
CLAUDE_API_KEY=your-claude-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here

# Background Removal Service API Keys
REMOVE_BG_API_KEY=your-remove-bg-api-key-here
PHOTOROOM_API_KEY=your-photoroom-api-key-here
CLIPDROP_API_KEY=your-clipdrop-api-key-here

# Default AI Providers
DEFAULT_LLM_PROVIDER=openai
DEFAULT_BG_REMOVAL_PROVIDER=cloudinary

# Cost Limits (USD)
DAILY_AI_COST_LIMIT=50.0
MONTHLY_AI_COST_LIMIT=500.0

# Shopify Integration (Optional)
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_STORE_URL=your-store.myshopify.com

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Processing Limits
MAX_CONCURRENT_DOWNLOADS=5
MAX_CONCURRENT_AI_REQUESTS=3

# Logging
LOG_LEVEL=INFO
LOG_FILE=dropshipping_dashboard.log

CLOUDINARY_URL=cloudinary://865235574489374:KH9BtTnnDDPKfTd2c7RzPcBJpdA@dylyjkwms
