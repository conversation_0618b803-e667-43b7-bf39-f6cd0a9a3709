"""
Order management routes
"""
from flask import Blueprint, request, jsonify
from services.mongodb_service import mongodb_service
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

orders_bp = Blueprint('orders', __name__)

@orders_bp.route('/', methods=['GET'])
def get_orders():
    """Get orders with pagination and filtering"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 50)), 100)
        skip = (page - 1) * limit
        
        # Build filters
        filters = {}
        
        # Status filter
        status = request.args.get('status')
        if status:
            filters['status'] = status
        
        # Date range filter
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter['$gte'] = start_date
            if end_date:
                date_filter['$lte'] = end_date
            filters['order_date'] = date_filter
        
        # Search filter
        search = request.args.get('search')
        if search:
            filters['$or'] = [
                {'order_id': {'$regex': search, '$options': 'i'}},
                {'buyer.fullName': {'$regex': search, '$options': 'i'}},
                {'buyer.country': {'$regex': search, '$options': 'i'}}
            ]
        
        # Get orders
        orders = mongodb_service.get_orders(skip=skip, limit=limit, filters=filters)
        
        # Get total count for pagination
        total = mongodb_service.db.orders.count_documents(filters)
        
        return jsonify({
            'success': True,
            'data': {
                'orders': orders,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'total_pages': (total + limit - 1) // limit
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting orders: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/<order_id>', methods=['GET'])
def get_order(order_id):
    """Get single order by ID"""
    try:
        order = mongodb_service.db.orders.find_one({'order_id': order_id})
        
        if not order:
            return jsonify({
                'success': False,
                'error': 'Order not found'
            }), 404
        
        # Convert ObjectId to string
        order['_id'] = str(order['_id'])
        
        return jsonify({
            'success': True,
            'data': order
        })
        
    except Exception as e:
        logger.error(f"Error getting order: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/stats')
def get_order_stats():
    """Get order statistics"""
    try:
        stats = {
            'total_orders': mongodb_service.db.orders.count_documents({}),
            'by_status': {},
            'by_country': {},
            'recent_orders': mongodb_service.db.orders.count_documents({
                'created_at': {'$gte': datetime.now() - timedelta(days=7)}
            }),
            'total_revenue': 0,
            'average_order_value': 0
        }
        
        # Get status distribution
        status_pipeline = [
            {'$group': {'_id': '$status', 'count': {'$sum': 1}}}
        ]
        status_results = list(mongodb_service.db.orders.aggregate(status_pipeline))
        for result in status_results:
            stats['by_status'][result['_id']] = result['count']
        
        # Get country distribution (top 10)
        country_pipeline = [
            {'$group': {'_id': '$buyer.country', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}},
            {'$limit': 10}
        ]
        country_results = list(mongodb_service.db.orders.aggregate(country_pipeline))
        for result in country_results:
            stats['by_country'][result['_id']] = result['count']
        
        # Calculate revenue statistics
        revenue_pipeline = [
            {'$group': {
                '_id': None,
                'total_revenue': {'$sum': '$payment.total'},
                'average_order_value': {'$avg': '$payment.total'},
                'order_count': {'$sum': 1}
            }}
        ]
        revenue_results = list(mongodb_service.db.orders.aggregate(revenue_pipeline))
        if revenue_results:
            result = revenue_results[0]
            stats['total_revenue'] = round(result.get('total_revenue', 0), 2)
            stats['average_order_value'] = round(result.get('average_order_value', 0), 2)
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting order stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/timeline')
def get_order_timeline():
    """Get order timeline data for charts"""
    try:
        days = int(request.args.get('days', 30))
        start_date = datetime.now() - timedelta(days=days)
        
        pipeline = [
            {'$match': {'created_at': {'$gte': start_date}}},
            {'$group': {
                '_id': {
                    'year': {'$year': '$created_at'},
                    'month': {'$month': '$created_at'},
                    'day': {'$dayOfMonth': '$created_at'}
                },
                'count': {'$sum': 1},
                'revenue': {'$sum': '$payment.total'}
            }},
            {'$sort': {'_id': 1}}
        ]
        
        results = list(mongodb_service.db.orders.aggregate(pipeline))
        
        timeline_data = []
        for result in results:
            date_obj = datetime(
                result['_id']['year'],
                result['_id']['month'],
                result['_id']['day']
            )
            timeline_data.append({
                'date': date_obj.strftime('%Y-%m-%d'),
                'orders': result['count'],
                'revenue': round(result['revenue'], 2)
            })
        
        return jsonify({
            'success': True,
            'data': timeline_data
        })
        
    except Exception as e:
        logger.error(f"Error getting order timeline: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/export', methods=['POST'])
def export_orders():
    """Export orders to CSV"""
    try:
        data = request.get_json()
        filters = data.get('filters', {})
        
        # Get orders based on filters
        orders = mongodb_service.get_orders(filters=filters, limit=10000)
        
        # Generate CSV
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Order ID', 'Order Date', 'Status', 'Buyer Name', 'Buyer Country',
            'Total Amount', 'Currency', 'Payment Method', 'Items Count'
        ])
        
        # Write data
        for order in orders:
            writer.writerow([
                order.get('order_id', ''),
                order.get('order_date', ''),
                order.get('status', ''),
                order.get('buyer', {}).get('fullName', ''),
                order.get('buyer', {}).get('country', ''),
                order.get('payment', {}).get('total', 0),
                order.get('payment', {}).get('currency', ''),
                order.get('payment', {}).get('method', ''),
                len(order.get('items', []))
            ])
        
        csv_data = output.getvalue()
        output.close()
        
        return jsonify({
            'success': True,
            'data': {
                'content': csv_data,
                'filename': f'orders_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
        })
        
    except Exception as e:
        logger.error(f"Error exporting orders: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500