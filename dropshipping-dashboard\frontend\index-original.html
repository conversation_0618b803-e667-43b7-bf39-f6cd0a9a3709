<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Rave - Dropshipping Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-200: #bae6fd;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;

            --rose-50: #fff1f2;
            --rose-100: #ffe4e6;
            --rose-200: #fecdd3;
            --rose-500: #f43f5e;

            --emerald-50: #ecfdf5;
            --emerald-100: #d1fae5;
            --emerald-200: #a7f3d0;
            --emerald-500: #10b981;
            --emerald-600: #059669;

            --violet-50: #f5f3ff;
            --violet-100: #ede9fe;
            --violet-200: #ddd6fe;
            --violet-500: #8b5cf6;

            --amber-50: #fffbeb;
            --amber-100: #fef3c7;
            --amber-200: #fde68a;
            --amber-500: #f59e0b;

            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            background: #ffffff;
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .header {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-600);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .nav-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn.primary {
            background: var(--primary-500);
            color: white;
        }

        .nav-btn.secondary {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .nav-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .nav-btn.primary:hover {
            background: var(--primary-600);
        }

        .nav-btn.secondary:hover {
            background: var(--gray-200);
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .main-content {
            background: var(--gray-50);
            margin: 0 -2rem;
            padding: 2rem;
            min-height: calc(100vh - 120px);
        }

        .dashboard-header {
            margin-bottom: 2rem;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            color: var(--gray-600);
            margin-bottom: 1.5rem;
        }

        .stats-section {
            background: white;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .stats-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .stats-header:hover {
            background: var(--gray-50);
        }

        .stats-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-toggle {
            color: var(--gray-400);
            transition: transform 0.2s ease;
        }

        .stats-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .stats-content {
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .stats-content.collapsed {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
        }

        .stat-card {
            background: var(--gray-50);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid var(--gray-200);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--emerald-500);
        }

        .stat-card:nth-child(2)::before {
            background: var(--violet-500);
        }

        .stat-card:nth-child(3)::before {
            background: var(--amber-500);
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .controls-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .controls-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .search-container {
            position: relative;
            max-width: 400px;
            flex: 1;
            margin: 0 2rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1rem;
        }

        .bulk-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .select-all-btn, .bulk-add-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .select-all-btn {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .bulk-add-btn {
            background: var(--emerald-500);
            color: white;
        }

        .bulk-add-btn:hover {
            background: var(--emerald-600);
            transform: translateY(-1px);
        }

        .select-all-btn:hover {
            background: var(--gray-200);
            transform: translateY(-1px);
        }

        .selected-count {
            font-weight: 500;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid var(--gray-200);
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: var(--gray-300);
        }

        .product-card.selected {
            border: 2px solid var(--primary-500);
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .product-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
            background: var(--gray-100);
        }

        .product-content {
            padding: 1rem;
        }

        .product-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 0.75rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--emerald-600);
            margin-bottom: 0.75rem;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-bottom: 1rem;
        }

        .product-checkbox {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            width: 20px;
            height: 20px;
            accent-color: var(--primary-500);
            cursor: pointer;
        }

        .product-actions {
            margin-top: 12px;
        }

        .add-btn {
            width: 100%;
            padding: 0.5rem;
            background: var(--primary-500);
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .add-btn:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
        }

        /* Progress Modal */
        .progress-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .progress-modal-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .progress-modal-content h3 {
            margin: 0 0 1.5rem 0;
            color: var(--gray-900);
            font-size: 1.25rem;
            font-weight: 600;
        }

        .progress-bar-container {
            background: var(--gray-200);
            border-radius: 8px;
            height: 8px;
            margin: 1rem 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-500), var(--violet-500));
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 8px;
        }

        #progress-text {
            color: var(--gray-600);
            font-size: 0.875rem;
            margin: 1rem 0 0 0;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray-600);
            font-size: 1rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .page-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            background: white;
            color: var(--gray-700);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .page-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
        }

        .page-btn.active {
            background: var(--primary-500);
            color: white;
            border-color: var(--primary-500);
        }

        .page-btn:first-child, .page-btn:last-child {
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .dashboard-title {
                font-size: 1.5rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .controls-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .bulk-actions {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .nav-buttons {
                flex-direction: column;
                gap: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                R.A.V.E
            </div>
            <nav class="nav-buttons">
                <a href="index.html" class="nav-btn primary">Dashboard</a>
                <a href="upload.html" class="nav-btn secondary">Upload Data</a>
                <a href="workspace.html" class="nav-btn secondary">Shopify Workspace</a>
            </nav>
        </div>
    </header>

    <main class="main-container">


        <div class="stats-section">
            <div class="stats-header" onclick="toggleStats()">
                <div class="stats-title">
                    <span>📊</span>
                    Dashboard Statistics
                </div>
                <span class="stats-toggle" id="stats-toggle">▼</span>
            </div>
            <div class="stats-content" id="stats-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-orders">0</div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="selected-products">0</div>
                        <div class="stat-label">Selected</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="ready-products">0</div>
                        <div class="stat-label">Shopify Ready</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">

            <div class="controls-section">
                <div class="controls-header">
                    <h2 class="controls-title">Product Selection</h2>
                    <div class="search-container">
                        <span class="search-icon">🔍</span>
                        <input type="text" class="search-input" placeholder="Search products by title, order ID, or price..." id="search-input">
                    </div>
                    <div class="bulk-actions">
                        <span class="selected-count" id="selected-count">0 selected</span>
                        <button class="select-all-btn" id="select-all-btn">Select All</button>
                        <button class="bulk-add-btn" id="bulk-add-btn">Extract Images & Prepare for Shopify</button>
                    </div>
                </div>
            </div>

            <div class="loading" id="loading">Loading products...</div>
            <div class="products-grid" id="products-grid" style="display: none;"></div>

            <div class="pagination" id="pagination" style="display: none;"></div>
        </div>
    </main>

    <script src="assets/js/app.js"></script>
    <script src="assets/js/dashboard-main.js"></script>

    <script>
        function toggleStats() {
            const content = document.getElementById('stats-content');
            const toggle = document.getElementById('stats-toggle');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.classList.remove('collapsed');
                toggle.textContent = '▼';
            } else {
                content.classList.add('collapsed');
                toggle.classList.add('collapsed');
                toggle.textContent = '▶';
            }
        }
    </script>
</body>
</html>
