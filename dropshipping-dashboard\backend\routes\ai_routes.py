"""
AI-powered content rewriting routes
"""
from flask import Blueprint, request, jsonify
from services.ai_content_service import AIContentService
from services.mongodb_service import mongodb_service
import logging

logger = logging.getLogger(__name__)

ai_bp = Blueprint('ai', __name__)
ai_service = AIContentService()

@ai_bp.route('/enhance-content', methods=['POST'])
def enhance_content():
    """Enhance content using AI"""
    try:
        data = request.get_json()

        if not data or 'content' not in data:
            return jsonify({
                'success': False,
                'error': 'Content is required'
            }), 400

        content = data['content']
        content_type = data.get('type', 'description')
        provider = data.get('provider', 'gemini')
        style = data.get('style', None)

        if not content.strip():
            return jsonify({
                'success': False,
                'error': 'Content cannot be empty'
            }), 400

        # Use the AI service to enhance content
        if content_type == 'description':
            result = ai_service.rewrite_description_direct(content, provider, style)
        elif content_type == 'title':
            result = ai_service.rewrite_title_direct(content, provider, style)
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid content type. Use "title" or "description"'
            }), 400

        if result['success']:
            return jsonify({
                'success': True,
                'data': {
                    'original_content': content,
                    'enhanced_content': result.get('content', result.get('rewritten_content', result.get('rewritten_title', result.get('rewritten_description', '')))),
                    'provider': provider,
                    'cost': result.get('cost', 0)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Failed to enhance content')
            }), 500

    except Exception as e:
        logger.error(f"Error enhancing content: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/rewrite/title/<product_id>', methods=['POST'])
def rewrite_title(product_id):
    """Rewrite product title using AI"""
    try:
        data = request.get_json() or {}
        provider = data.get('provider', 'openai')
        
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Get original title
        original_title = product.get('original_content', {}).get('title', '')
        if not original_title:
            return jsonify({
                'success': False,
                'error': 'No original title found'
            }), 400
        
        # Rewrite title
        result = ai_service.rewrite_title(product_id, provider)
        
        if result['success']:
            return jsonify({
                'success': True,
                'data': {
                    'original': original_title,
                    'rewritten': result['rewritten_title'],
                    'provider': provider,
                    'cost': result.get('cost', 0)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
    except Exception as e:
        logger.error(f"Error rewriting title: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/rewrite/description/<product_id>', methods=['POST'])
def rewrite_description(product_id):
    """Rewrite product description using AI"""
    try:
        data = request.get_json() or {}
        provider = data.get('provider', 'openai')
        
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Get original content
        original_title = product.get('original_content', {}).get('title', '')
        original_description = product.get('original_content', {}).get('description', original_title)
        
        if not original_title:
            return jsonify({
                'success': False,
                'error': 'No original content found'
            }), 400
        
        # Rewrite description
        result = ai_service.rewrite_description(product_id, provider)
        
        if result['success']:
            return jsonify({
                'success': True,
                'data': {
                    'original': original_description,
                    'rewritten': result['rewritten_description'],
                    'seo_keywords': result.get('seo_keywords', []),
                    'provider': provider,
                    'cost': result.get('cost', 0)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
    except Exception as e:
        logger.error(f"Error rewriting description: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/rewrite/bulk', methods=['POST'])
def bulk_rewrite():
    """Bulk rewrite content for multiple products"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        content_type = data.get('content_type', 'both')  # title, description, or both
        provider = data.get('provider', 'openai')
        
        # Start bulk processing
        result = ai_service.bulk_rewrite(product_ids, content_type, provider)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"Error in bulk rewrite: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/approve/<product_id>', methods=['POST'])
def approve_ai_content(product_id):
    """Approve AI-generated content"""
    try:
        data = request.get_json()
        
        if not data or 'content_type' not in data:
            return jsonify({
                'success': False,
                'error': 'content_type is required (title or description)'
            }), 400
        
        content_type = data['content_type']
        
        # Update product to mark content as approved
        update_data = {}
        if content_type == 'title':
            update_data['ai_content.title.user_approved'] = True
        elif content_type == 'description':
            update_data['ai_content.description.user_approved'] = True
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid content_type. Must be "title" or "description"'
            }), 400
        
        success = mongodb_service.update_product(product_id, update_data)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'{content_type.title()} approved successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to approve content'
            }), 500
        
    except Exception as e:
        logger.error(f"Error approving AI content: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/regenerate/<product_id>', methods=['POST'])
def regenerate_content(product_id):
    """Regenerate AI content with different parameters"""
    try:
        data = request.get_json()
        
        if not data or 'content_type' not in data:
            return jsonify({
                'success': False,
                'error': 'content_type is required'
            }), 400
        
        content_type = data['content_type']
        provider = data.get('provider', 'openai')
        custom_prompt = data.get('custom_prompt')
        
        # Regenerate content
        if content_type == 'title':
            result = ai_service.rewrite_title(product_id, provider, custom_prompt)
        elif content_type == 'description':
            result = ai_service.rewrite_description(product_id, provider, custom_prompt)
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid content_type'
            }), 400
        
        if result['success']:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
    except Exception as e:
        logger.error(f"Error regenerating content: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/status/<product_id>')
def get_ai_status(product_id):
    """Get AI processing status for a product"""
    try:
        product = mongodb_service.get_product(product_id)
        
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        ai_content = product.get('ai_content', {})
        
        status = {
            'title': {
                'status': ai_content.get('title', {}).get('rewrite_status', 'pending'),
                'provider': ai_content.get('title', {}).get('llm_provider', ''),
                'approved': ai_content.get('title', {}).get('user_approved', False),
                'rewritten': ai_content.get('title', {}).get('rewritten', ''),
                'timestamp': ai_content.get('title', {}).get('rewrite_timestamp')
            },
            'description': {
                'status': ai_content.get('description', {}).get('rewrite_status', 'pending'),
                'provider': ai_content.get('description', {}).get('llm_provider', ''),
                'approved': ai_content.get('description', {}).get('user_approved', False),
                'rewritten': ai_content.get('description', {}).get('rewritten', ''),
                'seo_keywords': ai_content.get('description', {}).get('seo_keywords', []),
                'timestamp': ai_content.get('description', {}).get('rewrite_timestamp')
            }
        }
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f"Error getting AI status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/providers')
def get_providers():
    """Get available AI providers and their status"""
    try:
        providers = ai_service.get_available_providers()
        
        return jsonify({
            'success': True,
            'data': providers
        })
        
    except Exception as e:
        logger.error(f"Error getting providers: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/providers/configure', methods=['POST'])
def configure_provider():
    """Configure AI provider settings"""
    try:
        data = request.get_json()
        
        if not data or 'provider' not in data:
            return jsonify({
                'success': False,
                'error': 'provider is required'
            }), 400
        
        provider = data['provider']
        settings = data.get('settings', {})
        
        # Update AI configuration
        success = mongodb_service.update_ai_config('content_rewriting', provider, {
            'provider': provider,
            'settings': settings,
            'is_active': True
        })
        
        if success:
            return jsonify({
                'success': True,
                'message': f'{provider} configured successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to configure provider'
            }), 500
        
    except Exception as e:
        logger.error(f"Error configuring provider: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/usage')
def get_usage_stats():
    """Get AI usage statistics"""
    try:
        stats = ai_service.get_usage_statistics()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting usage stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_bp.route('/test-connection', methods=['POST'])
def test_ai_connection():
    """Test AI provider connections"""
    try:
        data = request.get_json()
        provider = data.get('provider', 'openai')
        
        result = ai_service.test_connection(provider)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"Error testing AI connection: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500