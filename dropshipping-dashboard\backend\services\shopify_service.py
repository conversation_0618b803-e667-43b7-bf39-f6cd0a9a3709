"""
Shopify Integration Service for exporting products to Shopify store
"""
import requests
import logging
from typing import Dict, List, Optional
from datetime import datetime, timezone
import base64
import io
from PIL import Image

from config import Config
from services.mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class ShopifyService:
    """Service for integrating with Shopify API"""
    
    def __init__(self):
        self.config = Config()
        self.api_key = self.config.SHOPIFY_API_KEY
        self.api_secret = self.config.SHOPIFY_API_SECRET
        self.store_url = self.config.SHOPIFY_STORE_URL

        # Clean up store URL - remove any protocol prefix
        if self.store_url.startswith('https://'):
            self.store_url = self.store_url.replace('https://', '')
        if self.store_url.startswith('http://'):
            self.store_url = self.store_url.replace('http://', '')

        self.base_url = f"https://{self.store_url}/admin/api/2023-10/products.json"

        print(f"DEBUG: Shopify store_url: {self.store_url}")
        print(f"DEBUG: Shopify base_url: {self.base_url}")

        self.headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': self.api_secret
        }
        
        if not all([self.api_key, self.api_secret, self.store_url]):
            logger.warning("Shopify credentials not fully configured")
    
    def test_connection(self) -> Dict:
        """Test connection to Shopify API"""
        try:
            if not all([self.api_key, self.api_secret, self.store_url]):
                return {
                    'success': False,
                    'error': 'Shopify credentials not configured'
                }
            
            # Test with a simple shop info request
            test_url = f"https://{self.store_url}/admin/api/2023-10/shop.json"
            response = requests.get(test_url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                shop_data = response.json()
                return {
                    'success': True,
                    'shop_info': shop_data.get('shop', {}),
                    'message': 'Successfully connected to Shopify'
                }
            else:
                return {
                    'success': False,
                    'error': f'Shopify API error: {response.status_code} - {response.text}'
                }
                
        except Exception as e:
            logger.error(f"Error testing Shopify connection: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_product(self, product_id: str, selected_images: List[int] = None, main_image_index: int = 0) -> Dict:
        """Export a single product to Shopify"""
        try:
            # Get product from database
            product = mongodb_service.get_product(product_id)
            if not product:
                return {
                    'success': False,
                    'error': 'Product not found'
                }
            
            # Prepare product data for Shopify
            shopify_product = self._prepare_product_data(product, selected_images, main_image_index)
            
            # Create product in Shopify
            response = requests.post(
                self.base_url,
                json={'product': shopify_product},
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 201:
                shopify_data = response.json()
                shopify_product_id = shopify_data['product']['id']
                
                # Update product in database with Shopify info
                mongodb_service.update_product(product_id, {
                    'shopify_product_id': shopify_product_id,
                    'shopify_exported_at': datetime.now(timezone.utc),
                    'status': 'exported_to_shopify',
                    'updated_at': datetime.now(timezone.utc)
                })
                
                return {
                    'success': True,
                    'shopify_product_id': shopify_product_id,
                    'shopify_url': f"https://{self.store_url}/admin/products/{shopify_product_id}",
                    'message': 'Product exported to Shopify successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error exporting product {product_id} to Shopify: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def bulk_export_products(self, product_ids: List[str]) -> Dict:
        """Export multiple products to Shopify"""
        results = {
            'success': True,
            'exported_count': 0,
            'total_requested': len(product_ids),
            'errors': [],
            'exported_products': []
        }
        
        for product_id in product_ids:
            try:
                result = self.export_product(product_id)
                if result['success']:
                    results['exported_count'] += 1
                    results['exported_products'].append({
                        'product_id': product_id,
                        'shopify_product_id': result['shopify_product_id']
                    })
                else:
                    results['errors'].append(f"Product {product_id}: {result['error']}")
            except Exception as e:
                results['errors'].append(f"Product {product_id}: {str(e)}")
        
        if results['errors']:
            results['success'] = results['exported_count'] > 0
        
        return results
    
    def _prepare_product_data(self, product: Dict, selected_images: List[int] = None, main_image_index: int = 0) -> Dict:
        """Prepare product data for Shopify API"""
        
        # Get product images
        images = product.get('images', [])
        if selected_images is not None:
            # Filter to only selected images
            images = [images[i] for i in selected_images if i < len(images)]
        
        # Prepare images for Shopify
        shopify_images = []
        for i, image in enumerate(images):
            image_data = {
                'src': image.get('cloudinary_url', image.get('url', '')),
                'alt': f"{product.get('title', 'Product')} - Image {i+1}"
            }
            
            # Mark main image
            if i == main_image_index:
                image_data['position'] = 1
            
            shopify_images.append(image_data)
        
        # Calculate price with markup if available
        original_price = float(product.get('original_price', 0))
        final_price = float(product.get('final_price', original_price))
        
        # Prepare product data
        shopify_product = {
            'title': product.get('title', 'Untitled Product'),
            'body_html': product.get('description', ''),
            'vendor': 'R.A.V.E',
            'product_type': product.get('category', 'General'),
            'status': 'draft',  # Start as draft for review
            'images': shopify_images,
            'variants': [{
                'price': str(final_price),
                'compare_at_price': str(original_price) if original_price > final_price else None,
                'inventory_management': 'shopify',
                'inventory_quantity': 100,  # Default inventory
                'requires_shipping': True,
                'taxable': True
            }],
            'tags': [
                'imported',
                'aliexpress',
                product.get('currency', 'USD').upper()
            ]
        }
        
        return shopify_product
    
    def update_product_images(self, shopify_product_id: str, product_id: str, selected_images: List[int], main_image_index: int = 0) -> Dict:
        """Update images for an existing Shopify product"""
        try:
            # Get product from database
            product = mongodb_service.get_product(product_id)
            if not product:
                return {
                    'success': False,
                    'error': 'Product not found'
                }
            
            # Get product images
            images = product.get('images', [])
            selected_image_data = [images[i] for i in selected_images if i < len(images)]
            
            # Prepare images for Shopify
            shopify_images = []
            for i, image in enumerate(selected_image_data):
                image_data = {
                    'src': image.get('cloudinary_url', image.get('url', '')),
                    'alt': f"{product.get('title', 'Product')} - Image {i+1}"
                }
                
                # Mark main image
                if i == main_image_index:
                    image_data['position'] = 1
                
                shopify_images.append(image_data)
            
            # Update product in Shopify
            update_url = f"https://{self.store_url}/admin/api/2023-10/products/{shopify_product_id}.json"
            update_data = {
                'product': {
                    'id': shopify_product_id,
                    'images': shopify_images
                }
            }
            
            response = requests.put(
                update_url,
                json=update_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': 'Product images updated successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error updating Shopify product images: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_shopify_products(self, limit: int = 50, page_info: str = None, status: str = None) -> Dict:
        """Fetch products from Shopify store using cursor-based pagination"""
        try:
            # Build query parameters
            params = {
                'limit': min(limit, 250),  # Shopify limit
            }
            
            # Add page_info for cursor-based pagination
            if page_info:
                params['page_info'] = page_info
            
            if status:
                params['status'] = status
            
            # Make request to Shopify
            response = requests.get(
                self.base_url,
                params=params,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                products = data.get('products', [])
                
                # Process products for consistent format
                processed_products = []
                for product in products:
                    processed_product = self._process_shopify_product(product)
                    processed_products.append(processed_product)
                
                # Extract pagination info from Link header
                link_header = response.headers.get('Link', '')
                page_info_next = None
                page_info_prev = None
                
                if link_header:
                    # Parse Link header for pagination cursors
                    links = link_header.split(', ')
                    for link in links:
                        if 'rel="next"' in link:
                            # Extract page_info from next link
                            import re
                            match = re.search(r'page_info=([^&>]+)', link)
                            if match:
                                page_info_next = match.group(1)
                        elif 'rel="previous"' in link:
                            # Extract page_info from previous link
                            import re
                            match = re.search(r'page_info=([^&>]+)', link)
                            if match:
                                page_info_prev = match.group(1)
                
                return {
                    'success': True,
                    'products': processed_products,
                    'count': len(processed_products),
                    'page_info': {
                        'next': page_info_next,
                        'previous': page_info_prev
                    },
                    'limit': limit
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error fetching Shopify products: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_shopify_product(self, product_id: str) -> Dict:
        """Get a single product from Shopify"""
        try:
            url = f"https://{self.store_url}/admin/api/2023-10/products/{product_id}.json"
            
            response = requests.get(
                url,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                product = data.get('product', {})
                processed_product = self._process_shopify_product(product)
                
                return {
                    'success': True,
                    'product': processed_product
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error fetching Shopify product {product_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_shopify_product(self, product_id: str, updates: Dict) -> Dict:
        """Update a product in Shopify"""
        try:
            url = f"https://{self.store_url}/admin/api/2023-10/products/{product_id}.json"
            
            # Prepare update data
            update_data = {
                'product': {
                    'id': product_id,
                    **updates
                }
            }
            
            response = requests.put(
                url,
                json=update_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                updated_product = data.get('product', {})
                processed_product = self._process_shopify_product(updated_product)
                
                return {
                    'success': True,
                    'product': processed_product,
                    'message': 'Product updated successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error updating Shopify product {product_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_product_inventory(self, variant_id: str, inventory_quantity: int, inventory_management: str = 'shopify') -> Dict:
        """Update inventory for a product variant"""
        try:
            # First get the inventory item ID
            variant_url = f"https://{self.store_url}/admin/api/2023-10/variants/{variant_id}.json"
            variant_response = requests.get(variant_url, headers=self.headers, timeout=30)
            
            if variant_response.status_code != 200:
                return {
                    'success': False,
                    'error': f'Failed to get variant info: {variant_response.text}'
                }
            
            variant_data = variant_response.json()
            inventory_item_id = variant_data['variant']['inventory_item_id']
            
            # Update inventory management on variant if needed
            if inventory_management:
                variant_update_data = {
                    'variant': {
                        'id': variant_id,
                        'inventory_management': inventory_management
                    }
                }
                
                variant_update_response = requests.put(
                    variant_url,
                    json=variant_update_data,
                    headers=self.headers,
                    timeout=30
                )
                
                if variant_update_response.status_code != 200:
                    logger.warning(f"Failed to update inventory management: {variant_update_response.text}")
            
            # Get location ID (assuming first available location)
            locations_url = f"https://{self.store_url}/admin/api/2023-10/locations.json"
            locations_response = requests.get(locations_url, headers=self.headers, timeout=30)
            
            if locations_response.status_code != 200:
                return {
                    'success': False,
                    'error': f'Failed to get locations: {locations_response.text}'
                }
            
            locations_data = locations_response.json()
            locations = locations_data.get('locations', [])
            
            if not locations:
                return {
                    'success': False,
                    'error': 'No locations found in Shopify store'
                }
            
            location_id = locations[0]['id']
            
            # Update inventory level
            inventory_url = f"https://{self.store_url}/admin/api/2023-10/inventory_levels/set.json"
            inventory_data = {
                'location_id': location_id,
                'inventory_item_id': inventory_item_id,
                'available': inventory_quantity
            }
            
            inventory_response = requests.post(
                inventory_url,
                json=inventory_data,
                headers=self.headers,
                timeout=30
            )
            
            if inventory_response.status_code in [200, 201]:
                return {
                    'success': True,
                    'message': f'Inventory updated to {inventory_quantity}',
                    'inventory_quantity': inventory_quantity
                }
            else:
                error_msg = f"Shopify inventory API error: {inventory_response.status_code} - {inventory_response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error updating inventory for variant {variant_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_product_variant(self, product_id: str, variant_data: Dict) -> Dict:
        """Create a new variant for an existing product"""
        try:
            url = f"https://{self.store_url}/admin/api/2023-10/products/{product_id}/variants.json"
            
            create_data = {
                'variant': variant_data
            }
            
            response = requests.post(
                url,
                json=create_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 201:
                data = response.json()
                variant = data.get('variant', {})
                
                return {
                    'success': True,
                    'variant': variant,
                    'message': 'Variant created successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error creating variant for product {product_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_product_variant(self, variant_id: str, variant_updates: Dict) -> Dict:
        """Update an existing product variant"""
        try:
            url = f"https://{self.store_url}/admin/api/2023-10/variants/{variant_id}.json"
            
            update_data = {
                'variant': {
                    'id': variant_id,
                    **variant_updates
                }
            }
            
            response = requests.put(
                url,
                json=update_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                variant = data.get('variant', {})
                
                return {
                    'success': True,
                    'variant': variant,
                    'message': 'Variant updated successfully'
                }
            else:
                error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"Error updating variant {variant_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_shopify_product(self, product: Dict) -> Dict:
        """Process and normalize Shopify product data"""
        variants = product.get('variants', [])
        images = product.get('images', [])
        
        # Process variants
        processed_variants = []
        for variant in variants:
            processed_variant = {
                'id': variant.get('id'),
                'title': variant.get('title'),
                'price': float(variant.get('price', 0)),
                'compare_at_price': float(variant.get('compare_at_price', 0)) if variant.get('compare_at_price') else None,
                'sku': variant.get('sku'),
                'barcode': variant.get('barcode'),
                'inventory_quantity': variant.get('inventory_quantity', 0),
                'inventory_management': variant.get('inventory_management'),
                'inventory_policy': variant.get('inventory_policy'),
                'requires_shipping': variant.get('requires_shipping', True),
                'taxable': variant.get('taxable', True),
                'weight': variant.get('weight'),
                'weight_unit': variant.get('weight_unit'),
                'option1': variant.get('option1'),
                'option2': variant.get('option2'),
                'option3': variant.get('option3'),
                'created_at': variant.get('created_at'),
                'updated_at': variant.get('updated_at')
            }
            processed_variants.append(processed_variant)
        
        # Process images
        processed_images = []
        for image in images:
            processed_image = {
                'id': image.get('id'),
                'src': image.get('src'),
                'alt': image.get('alt'),
                'position': image.get('position'),
                'width': image.get('width'),
                'height': image.get('height')
            }
            processed_images.append(processed_image)
        
        # Process options
        options = product.get('options', [])
        processed_options = []
        for option in options:
            processed_option = {
                'id': option.get('id'),
                'name': option.get('name'),
                'position': option.get('position'),
                'values': option.get('values', [])
            }
            processed_options.append(processed_option)
        
        return {
            'id': product.get('id'),
            'title': product.get('title'),
            'body_html': product.get('body_html'),
            'vendor': product.get('vendor'),
            'product_type': product.get('product_type'),
            'handle': product.get('handle'),
            'status': product.get('status'),
            'published_scope': product.get('published_scope'),
            'tags': product.get('tags', '').split(',') if product.get('tags') else [],
            'variants': processed_variants,
            'images': processed_images,
            'options': processed_options,
            'created_at': product.get('created_at'),
            'updated_at': product.get('updated_at'),
            'published_at': product.get('published_at'),
            'template_suffix': product.get('template_suffix'),
            'admin_graphql_api_id': product.get('admin_graphql_api_id')
        }

# Create global instance
shopify_service = ShopifyService()
