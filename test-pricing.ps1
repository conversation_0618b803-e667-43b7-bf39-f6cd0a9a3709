# PowerShell script to test pricing functionality
$baseUri = "http://localhost:5000/api/products"

# Test 1: Calculate pricing
Write-Host "Testing pricing calculation..."
$calcUri = "$baseUri/calculate-pricing"
$calcBody = @{
    product_ids = @(
        "6884d76576dcaf567a7bac2b",
        "6884d76576dcaf567a7bac29"
    )
    markup_percentage = 50
    shipping_cost = 5.99
} | ConvertTo-<PERSON>son

try {
    $calcResponse = Invoke-RestMethod -Uri $calcUri -Method Post -Body $calcBody -ContentType "application/json"
    Write-Host "Pricing calculation successful:"
    $calcResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Pricing calculation failed:"
    Write-Host $_.Exception.Message
}

Write-Host "`n" + "="*50 + "`n"

# Test 2: Apply pricing
Write-Host "Testing pricing application..."
$applyUri = "$baseUri/apply-pricing"
$applyBody = @{
    markup_percentage = 50
    apply_to_all = $true
} | ConvertTo-Json

try {
    $applyResponse = Invoke-RestMethod -Uri $applyUri -Method Post -Body $applyBody -ContentType "application/json"
    Write-Host "Pricing application successful:"
    $applyResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Pricing application failed:"
    Write-Host $_.Exception.Message
}
