# PowerShell script to clear all test data from the database
$baseUri = "http://localhost:5000/api"

Write-Host "Clearing all test data from R.A.V.E Dashboard..." -ForegroundColor Yellow

# Step 1: Get all product IDs
Write-Host "`nStep 1: Getting all products..." -ForegroundColor Cyan
try {
    $productsResponse = Invoke-RestMethod -Uri "$baseUri/products/?limit=1000" -Method Get
    if ($productsResponse.success -and $productsResponse.data.products) {
        $productIds = $productsResponse.data.products | ForEach-Object { $_._id }
        Write-Host "Found $($productIds.Count) products to delete" -ForegroundColor Green
        
        if ($productIds.Count -gt 0) {
            # Step 2: Bulk delete all products
            Write-Host "`nStep 2: Deleting all products..." -ForegroundColor Cyan
            $deleteProductsBody = @{
                product_ids = $productIds
            } | ConvertTo-Json
            
            $deleteResponse = Invoke-RestMethod -Uri "$baseUri/products/bulk-delete" -Method Post -Body $deleteProductsBody -ContentType "application/json"
            Write-Host "Products deletion result:" -ForegroundColor Green
            $deleteResponse | ConvertTo-Json -Depth 2
        }
    } else {
        Write-Host "No products found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error getting/deleting products: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Clear orders (we'll need to do this via direct database access since there's no bulk delete for orders)
Write-Host "`nStep 3: Getting all orders..." -ForegroundColor Cyan
try {
    $ordersResponse = Invoke-RestMethod -Uri "$baseUri/orders/?limit=1000" -Method Get
    if ($ordersResponse.success -and $ordersResponse.data.orders) {
        Write-Host "Found $($ordersResponse.data.orders.Count) orders" -ForegroundColor Green
        Write-Host "Note: Orders need to be cleared manually via database or individual deletion" -ForegroundColor Yellow
    } else {
        Write-Host "No orders found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error getting orders: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nData clearing process completed!" -ForegroundColor Green
Write-Host "You can now upload your real AliExpress data." -ForegroundColor Cyan
