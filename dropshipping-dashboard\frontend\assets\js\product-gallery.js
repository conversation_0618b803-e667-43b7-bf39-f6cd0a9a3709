/* Enhanced Product Gallery Component */

class ProductGallery {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            itemsPerPage: 12,
            enableLightbox: true,
            enableFilters: true,
            enableSearch: true,
            apiUrl: 'http://127.0.0.1:5000/api',
            ...options
        };
        
        this.state = {
            products: [],
            filteredProducts: [],
            currentPage: 1,
            totalPages: 1,
            isLoading: false,
            searchQuery: '',
            selectedFilters: {
                status: 'all',
                priceRange: 'all',
                category: 'all'
            }
        };
        
        this.init();
    }
    
    async init() {
        this.createGalleryStructure();
        this.attachEventListeners();
        await this.loadProducts();
        this.render();
    }
    
    createGalleryStructure() {
        this.container.innerHTML = `
            <div class="gallery-header">
                <div class="gallery-controls">
                    <div class="search-container">
                        <input type="text" id="product-search" placeholder="Search products..." class="search-input">
                        <button class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="filter-controls">
                        <select id="status-filter" class="filter-select">
                            <option value="all">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                        </select>
                        <select id="price-filter" class="filter-select">
                            <option value="all">All Prices</option>
                            <option value="0-25">$0 - $25</option>
                            <option value="25-50">$25 - $50</option>
                            <option value="50-100">$50 - $100</option>
                            <option value="100+">$100+</option>
                        </select>
                    </div>
                </div>
                <div class="gallery-stats">
                    <span class="product-count">0 products</span>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-content">
                <div class="products-grid" id="products-grid">
                    <!-- Products will be rendered here -->
                </div>
                
                <div class="gallery-pagination">
                    <button class="pagination-btn" id="prev-page" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>
                    <div class="page-numbers" id="page-numbers">
                        <!-- Page numbers will be rendered here -->
                    </div>
                    <button class="pagination-btn" id="next-page" disabled>
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- Lightbox Modal -->
            <div class="lightbox-modal hidden" id="lightbox-modal">
                <div class="lightbox-overlay"></div>
                <div class="lightbox-content">
                    <button class="lightbox-close">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="lightbox-image-container">
                        <img class="lightbox-image" src="" alt="">
                        <div class="lightbox-nav">
                            <button class="lightbox-prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="lightbox-next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    <div class="lightbox-info">
                        <h3 class="lightbox-title"></h3>
                        <p class="lightbox-description"></p>
                        <div class="lightbox-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-edit"></i>
                                Edit Product
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Download Images
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    attachEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('product-search');
        searchInput.addEventListener('input', this.debounce((e) => {
            this.state.searchQuery = e.target.value;
            this.filterProducts();
        }, 300));
        
        // Filter functionality
        document.getElementById('status-filter').addEventListener('change', (e) => {
            this.state.selectedFilters.status = e.target.value;
            this.filterProducts();
        });
        
        document.getElementById('price-filter').addEventListener('change', (e) => {
            this.state.selectedFilters.priceRange = e.target.value;
            this.filterProducts();
        });
        
        // View toggle
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.view-btn').classList.add('active');
                this.toggleView(e.target.closest('.view-btn').dataset.view);
            });
        });
        
        // Pagination
        document.getElementById('prev-page').addEventListener('click', () => {
            if (this.state.currentPage > 1) {
                this.state.currentPage--;
                this.render();
            }
        });
        
        document.getElementById('next-page').addEventListener('click', () => {
            if (this.state.currentPage < this.state.totalPages) {
                this.state.currentPage++;
                this.render();
            }
        });
        
        // Lightbox
        this.attachLightboxListeners();
    }
    
    attachLightboxListeners() {
        const modal = document.getElementById('lightbox-modal');
        const overlay = modal.querySelector('.lightbox-overlay');
        const closeBtn = modal.querySelector('.lightbox-close');
        
        [overlay, closeBtn].forEach(element => {
            element.addEventListener('click', () => {
                this.closeLightbox();
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (!modal.classList.contains('hidden')) {
                if (e.key === 'Escape') this.closeLightbox();
                if (e.key === 'ArrowLeft') this.lightboxPrev();
                if (e.key === 'ArrowRight') this.lightboxNext();
            }
        });
    }
    
    async loadProducts() {
        this.showLoading();
        try {
            const response = await fetch(`${this.options.apiUrl}/products`);
            const data = await response.json();
            
            if (data.success) {
                this.state.products = data.products || [];
                this.state.filteredProducts = [...this.state.products];
                this.calculatePagination();
                this.updateStats();
            } else {
                throw new Error(data.message || 'Failed to load products');
            }
        } catch (error) {
            console.error('Error loading products:', error);
            this.showError('Failed to load products. Please try again.');
        } finally {
            this.hideLoading();
        }
    }
    
    filterProducts() {
        let filtered = [...this.state.products];
        
        // Search filter
        if (this.state.searchQuery) {
            const query = this.state.searchQuery.toLowerCase();
            filtered = filtered.filter(product => 
                product.title?.toLowerCase().includes(query) ||
                product.description?.toLowerCase().includes(query)
            );
        }
        
        // Status filter
        if (this.state.selectedFilters.status !== 'all') {
            filtered = filtered.filter(product => 
                product.status === this.state.selectedFilters.status
            );
        }
        
        // Price filter
        if (this.state.selectedFilters.priceRange !== 'all') {
            const [min, max] = this.state.selectedFilters.priceRange.split('-').map(p => 
                p === '+' ? Infinity : parseFloat(p)
            );
            filtered = filtered.filter(product => {
                const price = parseFloat(product.price) || 0;
                return max === undefined ? price >= min : price >= min && price <= max;
            });
        }
        
        this.state.filteredProducts = filtered;
        this.state.currentPage = 1;
        this.calculatePagination();
        this.updateStats();
        this.render();
    }
    
    calculatePagination() {
        this.state.totalPages = Math.ceil(this.state.filteredProducts.length / this.options.itemsPerPage);
        if (this.state.currentPage > this.state.totalPages) {
            this.state.currentPage = 1;
        }
    }
    
    updateStats() {
        const countElement = document.querySelector('.product-count');
        if (countElement) {
            const count = this.state.filteredProducts.length;
            countElement.textContent = `${count} product${count !== 1 ? 's' : ''}`;
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    showLoading() {
        this.state.isLoading = true;
        const grid = document.getElementById('products-grid');
        grid.innerHTML = this.createSkeletonLoader();
    }
    
    hideLoading() {
        this.state.isLoading = false;
    }
    
    showError(message) {
        const grid = document.getElementById('products-grid');
        grid.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error Loading Products</h3>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i>
                    Retry
                </button>
            </div>
        `;
    }
    
    createSkeletonLoader() {
        return Array(this.options.itemsPerPage).fill(0).map(() => `
            <div class="product-card skeleton-card">
                <div class="skeleton skeleton-card"></div>
            </div>
        `).join('');
    }
}

    render() {
        if (this.state.isLoading) return;

        const grid = document.getElementById('products-grid');
        const startIndex = (this.state.currentPage - 1) * this.options.itemsPerPage;
        const endIndex = startIndex + this.options.itemsPerPage;
        const pageProducts = this.state.filteredProducts.slice(startIndex, endIndex);

        if (pageProducts.length === 0) {
            grid.innerHTML = this.createEmptyState();
            this.renderPagination();
            return;
        }

        grid.innerHTML = pageProducts.map((product, index) =>
            this.createProductCard(product, startIndex + index)
        ).join('');

        this.renderPagination();
        this.attachProductEventListeners();
    }

    createProductCard(product, index) {
        const images = product.images || [];
        const mainImage = images[0] || '/assets/images/placeholder.jpg';
        const price = parseFloat(product.price) || 0;
        const originalPrice = parseFloat(product.original_price) || price;

        return `
            <div class="product-card" data-product-index="${index}">
                <div class="product-image-container">
                    <img class="product-image" src="${mainImage}" alt="${product.title || 'Product'}"
                         onerror="this.src='/assets/images/placeholder.jpg'">
                    <div class="product-image-overlay"></div>
                    <div class="product-actions">
                        <button class="product-action-btn" data-action="view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="product-action-btn" data-action="edit" title="Edit Product">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="product-action-btn" data-action="download" title="Download Images">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                    ${images.length > 1 ? `<div class="image-count"><i class="fas fa-images"></i> ${images.length}</div>` : ''}
                </div>
                <div class="product-content">
                    <h3 class="product-title">${product.title || 'Untitled Product'}</h3>
                    <div class="product-price">
                        ${originalPrice > price ? `<span class="price-original">$${originalPrice.toFixed(2)}</span>` : ''}
                        <span class="price-final">$${price.toFixed(2)}</span>
                    </div>
                    <div class="product-meta">
                        <span class="product-status status-${product.status || 'pending'}">
                            <i class="fas fa-circle"></i>
                            ${(product.status || 'pending').charAt(0).toUpperCase() + (product.status || 'pending').slice(1)}
                        </span>
                        ${product.order_date ? `<span class="product-date">${new Date(product.order_date).toLocaleDateString()}</span>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    createEmptyState() {
        return `
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <h3>No Products Found</h3>
                <p>Try adjusting your search or filter criteria.</p>
                <button class="btn btn-primary" onclick="this.clearFilters()">
                    <i class="fas fa-refresh"></i>
                    Clear Filters
                </button>
            </div>
        `;
    }

    renderPagination() {
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        const pageNumbers = document.getElementById('page-numbers');

        // Update navigation buttons
        prevBtn.disabled = this.state.currentPage <= 1;
        nextBtn.disabled = this.state.currentPage >= this.state.totalPages;

        // Generate page numbers
        const pages = this.generatePageNumbers();
        pageNumbers.innerHTML = pages.map(page => {
            if (page === '...') {
                return '<span class="pagination-ellipsis">...</span>';
            }
            return `
                <button class="pagination-btn ${page === this.state.currentPage ? 'active' : ''}"
                        data-page="${page}">
                    ${page}
                </button>
            `;
        }).join('');

        // Attach page number event listeners
        pageNumbers.querySelectorAll('.pagination-btn[data-page]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.state.currentPage = parseInt(e.target.dataset.page);
                this.render();
            });
        });
    }

    generatePageNumbers() {
        const current = this.state.currentPage;
        const total = this.state.totalPages;
        const pages = [];

        if (total <= 7) {
            for (let i = 1; i <= total; i++) {
                pages.push(i);
            }
        } else {
            pages.push(1);

            if (current <= 4) {
                for (let i = 2; i <= 5; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(total);
            } else if (current >= total - 3) {
                pages.push('...');
                for (let i = total - 4; i <= total; i++) {
                    pages.push(i);
                }
            } else {
                pages.push('...');
                for (let i = current - 1; i <= current + 1; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(total);
            }
        }

        return pages;
    }

    attachProductEventListeners() {
        document.querySelectorAll('.product-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = e.target.closest('.product-action-btn').dataset.action;
                const card = e.target.closest('.product-card');
                const productIndex = parseInt(card.dataset.productIndex);
                const product = this.state.filteredProducts[productIndex];

                this.handleProductAction(action, product, productIndex);
            });
        });

        // Click on product card to open lightbox
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.product-action-btn')) {
                    const productIndex = parseInt(card.dataset.productIndex);
                    const product = this.state.filteredProducts[productIndex];
                    this.openLightbox(product, productIndex);
                }
            });
        });
    }

    handleProductAction(action, product, index) {
        switch (action) {
            case 'view':
                this.openLightbox(product, index);
                break;
            case 'edit':
                this.editProduct(product);
                break;
            case 'download':
                this.downloadProductImages(product);
                break;
        }
    }

    openLightbox(product, index) {
        if (!this.options.enableLightbox) return;

        const modal = document.getElementById('lightbox-modal');
        const image = modal.querySelector('.lightbox-image');
        const title = modal.querySelector('.lightbox-title');
        const description = modal.querySelector('.lightbox-description');

        this.currentLightboxProduct = product;
        this.currentLightboxIndex = index;
        this.currentImageIndex = 0;

        const images = product.images || [];
        if (images.length > 0) {
            image.src = images[0];
        }

        title.textContent = product.title || 'Untitled Product';
        description.textContent = product.description || 'No description available.';

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Add entrance animation
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.lightbox-content').style.transform = 'scale(1)';
        }, 10);
    }

    closeLightbox() {
        const modal = document.getElementById('lightbox-modal');

        modal.style.opacity = '0';
        modal.querySelector('.lightbox-content').style.transform = 'scale(0.9)';

        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }, 300);
    }

    lightboxPrev() {
        if (!this.currentLightboxProduct) return;

        const images = this.currentLightboxProduct.images || [];
        if (images.length <= 1) return;

        this.currentImageIndex = (this.currentImageIndex - 1 + images.length) % images.length;
        this.updateLightboxImage();
    }

    lightboxNext() {
        if (!this.currentLightboxProduct) return;

        const images = this.currentLightboxProduct.images || [];
        if (images.length <= 1) return;

        this.currentImageIndex = (this.currentImageIndex + 1) % images.length;
        this.updateLightboxImage();
    }

    updateLightboxImage() {
        const modal = document.getElementById('lightbox-modal');
        const image = modal.querySelector('.lightbox-image');
        const images = this.currentLightboxProduct.images || [];

        if (images[this.currentImageIndex]) {
            image.style.opacity = '0';
            setTimeout(() => {
                image.src = images[this.currentImageIndex];
                image.style.opacity = '1';
            }, 150);
        }
    }

    toggleView(view) {
        const grid = document.getElementById('products-grid');
        grid.className = view === 'list' ? 'products-list' : 'products-grid';
    }

    clearFilters() {
        this.state.searchQuery = '';
        this.state.selectedFilters = {
            status: 'all',
            priceRange: 'all',
            category: 'all'
        };

        document.getElementById('product-search').value = '';
        document.getElementById('status-filter').value = 'all';
        document.getElementById('price-filter').value = 'all';

        this.filterProducts();
    }

    editProduct(product) {
        // Emit custom event for product editing
        window.dispatchEvent(new CustomEvent('editProduct', { detail: product }));
    }

    downloadProductImages(product) {
        // Emit custom event for image download
        window.dispatchEvent(new CustomEvent('downloadProductImages', { detail: product }));
    }
}

// Export for use in other files
window.ProductGallery = ProductGallery;
