<!DOCTYPE html>
<html>
<head>
    <title>Test Images</title>
</head>
<body>
    <h1>Testing Image URLs</h1>
    <div id="results"></div>
    
    <script>
        async function testImages() {
            try {
                // Get workspace products
                const response = await fetch('http://localhost:5000/api/workspace/products');
                const data = await response.json();
                
                console.log('API Response:', data);
                
                const resultsDiv = document.getElementById('results');
                
                if (data.success && data.data.products.length > 0) {
                    const product = data.data.products[0];
                    console.log('First product:', product);
                    
                    resultsDiv.innerHTML = `
                        <h2>Product: ${product.title}</h2>
                        <p>Product ID: ${product._id}</p>
                        <p>Images count: ${product.images ? product.images.length : 0}</p>
                    `;
                    
                    if (product.images && product.images.length > 0) {
                        product.images.forEach((img, index) => {
                            console.log(`Image ${index + 1}:`, img);
                            
                            let imageUrl = img.url;
                            if (imageUrl.startsWith('/api/')) {
                                imageUrl = 'http://localhost:5000' + imageUrl.substring(4);
                            }
                            
                            resultsDiv.innerHTML += `
                                <div style="margin: 10px 0; border: 1px solid #ccc; padding: 10px;">
                                    <p><strong>Image ${index + 1}:</strong></p>
                                    <p>Original URL: ${img.url}</p>
                                    <p>Constructed URL: ${imageUrl}</p>
                                    <p>Local Path: ${img.local_path || 'N/A'}</p>
                                    <img src="${imageUrl}" alt="Test Image" style="max-width: 200px; border: 1px solid red;" 
                                         onload="console.log('Image loaded:', this.src)" 
                                         onerror="console.error('Image failed:', this.src)">
                                </div>
                            `;
                        });
                    }
                } else {
                    resultsDiv.innerHTML = '<p>No products found in workspace</p>';
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('results').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testImages();
    </script>
</body>
</html>
