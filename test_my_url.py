#!/usr/bin/env python3
"""
Test script for downloading images from a specific AliExpress URL

Instructions:
1. Replace YOUR_URL_HERE with your actual AliExpress product URL
2. Run: python test_my_url.py
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from aliexpress_image_downloader import AliExpressImageDownloader
    print("✅ Successfully imported AliExpressImageDownloader")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies: pip install -r requirements.txt")
    sys.exit(1)


def test_single_url():
    """Test downloading from a single URL"""
    
    # 🔧 REPLACE THIS URL WITH YOUR ALIEXPRESS PRODUCT URL
    test_url = "https://www.aliexpress.com/item/3256808519179538.html"
    
    # Check if URL was updated
    if "YOUR_PRODUCT_ID" in test_url:
        print("❌ Please update the test_url variable with your actual AliExpress URL")
        print("Edit this file and replace YOUR_PRODUCT_ID with the actual product ID")
        return False
    
    print(f"🧪 Testing URL: {test_url}")
    
    # Create downloader
    downloader = AliExpressImageDownloader(
        download_dir="my_test_download",
        max_workers=3
    )
    
    # Validate URL first
    if not downloader.validate_url(test_url):
        print("❌ Invalid AliExpress URL")
        return False
    
    print("✅ URL validation passed")
    
    # Extract product ID
    product_id = downloader.extract_product_id(test_url)
    print(f"📦 Product ID: {product_id}")
    
    # Download images
    print("🚀 Starting download...")
    success = downloader.download_from_url(test_url)
    
    if success:
        print("✅ Download completed successfully!")
        
        # Check results
        download_dir = Path("my_test_download")
        if download_dir.exists():
            folders = list(download_dir.iterdir())
            if folders:
                product_folder = folders[0]
                images = list(product_folder.glob("*.jpg")) + list(product_folder.glob("*.png"))
                metadata_file = product_folder / "metadata.json"
                
                print(f"📁 Download folder: {product_folder}")
                print(f"🖼️  Images downloaded: {len(images)}")
                print(f"📄 Metadata file: {'✅' if metadata_file.exists() else '❌'}")
                
                if images:
                    print("📋 Downloaded files:")
                    for img in images:
                        print(f"   - {img.name}")
                        
        return True
    else:
        print("❌ Download failed")
        return False


def main():
    """Main function"""
    print("🧪 AliExpress Image Downloader - Single URL Test")
    print("=" * 50)
    
    try:
        success = test_single_url()
        
        if success:
            print("\n" + "=" * 50)
            print("✅ Test completed successfully!")
            print("Check the 'my_test_download' folder for your images")
        else:
            print("\n" + "=" * 50)
            print("❌ Test failed")
            print("Please check the URL and try again")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1
        
    return 0


if __name__ == "__main__":
    sys.exit(main())
