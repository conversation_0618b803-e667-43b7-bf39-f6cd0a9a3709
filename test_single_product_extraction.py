#!/usr/bin/env python3
"""
Test script to extract images from a single product
"""
import requests
import json
import time

def test_image_extraction():
    print("🧪 Testing Image Extraction for Single Product")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Step 1: Get a product to test with
    print("\n1. Getting a sample product...")
    try:
        response = requests.get(f"{base_url}/api/products/?limit=1")
        if response.status_code != 200:
            print(f"❌ Failed to get products: {response.status_code}")
            return
        
        data = response.json()
        if not data.get('success') or not data.get('data', {}).get('products'):
            print("❌ No products found")
            return
        
        product = data['data']['products'][0]
        product_id = product['_id']
        title = product.get('title', 'Unknown')
        aliexpress_url = product.get('aliexpress_data', {}).get('original_url', '')
        current_images = len(product.get('images', []))
        
        print(f"✅ Found product:")
        print(f"   - ID: {product_id}")
        print(f"   - Title: {title}")
        print(f"   - AliExpress URL: {aliexpress_url}")
        print(f"   - Current Images: {current_images}")
        
        if not aliexpress_url:
            print("❌ No AliExpress URL found")
            return
            
    except Exception as e:
        print(f"❌ Error getting product: {e}")
        return
    
    # Step 2: Test image extraction
    print(f"\n2. Testing image extraction for product {product_id}...")
    try:
        response = requests.post(f"{base_url}/api/images/extract/{product_id}")
        
        print(f"   - Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   - Success: {result.get('success')}")
            
            if result.get('success'):
                print("✅ Image extraction successful!")
                data = result.get('data', {})
                print(f"   - Extracted images: {data.get('extracted_count', 0)}")
                print(f"   - Message: {data.get('message', 'N/A')}")
            else:
                print(f"❌ Image extraction failed: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   - Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   - Raw response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return
    
    # Step 3: Check updated product
    print(f"\n3. Checking updated product...")
    try:
        response = requests.get(f"{base_url}/api/products/?limit=1")
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data', {}).get('products'):
                updated_product = data['data']['products'][0]
                new_images = len(updated_product.get('images', []))
                status = updated_product.get('status', 'unknown')
                
                print(f"✅ Product updated:")
                print(f"   - Status: {status}")
                print(f"   - Images after extraction: {new_images}")
                print(f"   - Images added: {new_images - current_images}")
                
                # Show image details
                images = updated_product.get('images', [])
                for i, img in enumerate(images[:3]):  # Show first 3 images
                    print(f"   - Image {i+1}: {img.get('url', 'N/A')[:60]}...")
                    
    except Exception as e:
        print(f"❌ Error checking updated product: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Test completed!")

if __name__ == "__main__":
    test_image_extraction()
