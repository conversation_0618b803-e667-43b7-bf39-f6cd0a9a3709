/* Main CSS for Dropshipping Dashboard */

/* Shop Rave - Minimalist Dashboard CSS */

/* MatterSq Font Import */
@font-face {
    font-family: "Matter SQ";
    src: url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.eot");
    src: url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.eot?#iefix") format("embedded-opentype"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.woff") format("woff"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.woff2") format("woff2"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.ttf") format("truetype"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.svg#Matter SQ Regular") format("svg");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Minimalist Design Variables */
:root {
    /* Clean Color Palette */
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-primary: "Matter SQ", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    --font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, monospace;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

}

/* Reset & Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 14px;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.25;
    color: var(--gray-900);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Layout Components */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    height: var(--header-height);
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-6);
    z-index: 40;
    transition: left var(--transition);
}

.header.expanded {
    left: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.header-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.header-title i {
    color: var(--primary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    z-index: 50;
    transition: transform var(--transition);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--gray-200);
    padding: 0 var(--space-6);
}

.logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--primary);
    text-decoration: none;
}

.nav-menu {
    padding: var(--space-6) 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--gray-600);
    text-decoration: none;
    transition: all var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.nav-item.active {
    background: var(--primary);
    color: var(--white);
    border-left-color: var(--primary-dark);
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: var(--space-8);
    min-height: calc(100vh - var(--header-height));
    transition: margin-left var(--transition);
}

.main-content.expanded {
    margin-left: 0;
}

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-gradient);
    background-attachment: fixed;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
}

/* Utility Classes */
.hidden { display: none !important; }
.visible { display: block !important; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--text-white); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }

.border { border: 1px solid var(--border-light); }
.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-dark { border-color: var(--border-dark); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Enhanced Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-white);
    border: none;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-gradient-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
    backdrop-filter: var(--glass-backdrop);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--text-white);
    border: none;
    box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-warning {
    background: var(--warning-gradient);
    color: var(--text-white);
    border: none;
    box-shadow: var(--shadow-md);
}

.btn-warning:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-error {
    background: var(--error-gradient);
    color: var(--text-white);
    border: none;
    box-shadow: var(--shadow-md);
}

.btn-error:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
}

.btn-xl {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
    border-radius: var(--radius-xl);
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

.search-input {
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: var(--spacing-xl);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    background-color: var(--bg-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Enhanced Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: all var(--transition-normal);
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.9);
}

.loading-spinner {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--glass-border);
    animation: fadeInScale 0.3s ease-out;
    color: var(--text-white);
}

.loading-spinner i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: bounce 1s infinite;
}

.loading-spinner p {
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
    color: var(--text-white);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--spacing-sm);
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
    width: 60%;
}

.skeleton-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    border-radius: var(--radius-lg);
}

/* Progress Bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.progress-bar.large {
    height: 12px;
}

.progress-bar.small {
    height: 4px;
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.loading-spinner p {
    margin: 0;
    color: var(--text-secondary);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-title i {
    color: var(--primary-color);
}

.header-center {
    flex: 1;
    max-width: 500px;
    margin: 0 var(--spacing-xl);
}

.search-container {
    position: relative;
}

.search-container .search-input {
    width: 100%;
    padding-right: 3rem;
}

.search-btn {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-stats {
    display: flex;
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    display: block;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.notification-bell {
    position: relative;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.notification-bell:hover {
    background-color: var(--bg-tertiary);
}

.notification-bell i {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--error-color);
    color: var(--text-white);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 1.25rem;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
}

.user-avatar:hover {
    background-color: var(--primary-hover);
}

/* Main Container */
.main-container {
    display: flex;
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: var(--header-height);
    left: 0;
    bottom: 0;
    overflow-y: auto;
    transition: transform var(--transition-normal);
    z-index: 999;
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-item.active .nav-link {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.nav-link i {
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.nav-badge {
    margin-left: auto;
    background-color: var(--primary-color);
    color: var(--text-white);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 1.25rem;
    text-align: center;
}

.nav-item.active .nav-badge {
    background-color: rgba(255, 255, 255, 0.2);
}

.connection-status {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-indicator {
    margin-left: auto;
}

.status-indicator i {
    font-size: 0.75rem;
    color: var(--success-color);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: var(--spacing-xl);
    transition: margin-left var(--transition-normal);
}

.main-content.expanded {
    margin-left: 0;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.section-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Cards */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: box-shadow var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 300px;
    animation: toastSlideIn 0.3s ease-out;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

.toast-icon {
    font-size: 1.25rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.toast-message {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Placeholder Content */
.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-muted);
}

.placeholder-content i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.placeholder-content p {
    font-size: 1.125rem;
    margin: 0;
}