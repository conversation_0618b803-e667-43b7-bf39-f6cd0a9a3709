<div className="w-[1440px] h-[932px] bg-white rounded-[30px] shadow-[0px_44px_84px_6px_rgba(216,217,219,1.00)]" />
<div className="w-56 h-10 relative">
  <div className="w-4 h-4 left-[15px] top-[13px] absolute">
    <div className="w-1.5 h-1.5 left-[2.54px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[3.39px] h-1.5 left-[10.38px] top-[2.17px] absolute bg-zinc-500" />
    <div className="w-2.5 h-1.5 left-[1.07px] top-[8.29px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.09px] h-[5.01px] left-[11.73px] top-[8.83px] absolute bg-zinc-500" />
  </div>
  <div className="w-24 h-10 left-0 top-0 absolute rounded-md border border-zinc-500" />
  <div className="left-[37px] top-[11px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter'] capitalize">Share</div>
  <div className="w-10 h-10 left-[137px] top-0 absolute bg-indigo-600 rounded-md" />
  <div className="w-5 h-5 left-[147px] top-[30px] absolute origin-top-left -rotate-90">
    <div className="w-3.5 h-1.5 left-[2.50px] top-[11.13px] absolute bg-white outline outline-[1.50px] outline-offset-[-0.75px]" />
    <div className="w-3.5 h-1.5 left-[2.50px] top-[2.50px] absolute bg-white outline outline-[1.50px] outline-offset-[-0.75px]" />
    <div className="w-5 h-5 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="w-5 h-5 left-[199px] top-[10px] absolute">
    <div className="w-[5px] h-[5px] left-[12.69px] top-[3px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[5px] h-[5px] left-[3px] top-[3px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[5px] h-[5px] left-[12.69px] top-[13px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[5px] h-[5px] left-[3px] top-[13px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-5 h-5 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="w-0 h-7 left-[117px] top-[5.50px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
</div>
<div className="w-20 h-6 relative">
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-2.5 h-2.5 left-[1.25px] top-[1.25px] absolute bg-zinc-500" />
    <div className="w-2.5 h-2.5 left-[13.25px] top-[1.25px] absolute bg-zinc-500" />
    <div className="w-2.5 h-2.5 left-[13.25px] top-[13.25px] absolute bg-zinc-500" />
    <div className="w-2.5 h-2.5 left-[1.25px] top-[13.25px] absolute bg-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[38px] top-[2px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Home</div>
</div>
<div className="w-20 h-6 relative">
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-1.5 h-0 left-[12.37px] top-[8.88px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[3px] h-0.5 left-[6.38px] top-[7.38px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-1.5 h-0 left-[12.37px] top-[15.88px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[3px] h-0.5 left-[6.38px] top-[14.38px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-5 h-5 left-[2px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[38px] top-[2px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Tasks</div>
</div>
<div className="w-28 h-6 relative">
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-5 h-5 left-[2px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-px h-px left-[15.50px] top-[10.50px] absolute outline outline-2 outline-offset-[-1px] outline-zinc-500" />
    <div className="w-px h-px left-[11.50px] top-[10.50px] absolute outline outline-2 outline-offset-[-1px] outline-zinc-500" />
    <div className="w-px h-px left-[7.50px] top-[10.50px] absolute outline outline-2 outline-offset-[-1px] outline-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[38px] top-[2px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Messages</div>
</div>
<div className="w-28 h-6 relative">
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-2 h-2 left-[4.56px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-1 h-1.5 left-[16.28px] top-[4px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-3.5 h-2 left-[2.34px] top-[13.19px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[3.13px] h-1.5 left-[18.34px] top-[14px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-6 h-6 left-[24px] top-[24px] absolute origin-top-left -rotate-180 opacity-0" />
  </div>
  <div className="left-[38px] top-[2px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Members</div>
</div>
<div className="w-24 h-6 relative">
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-1.5 h-1.5 left-[9px] top-[9px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-5 h-5 left-[2px] top-[2.56px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[39px] top-[2px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Settings</div>
</div>
<div className="w-0 h-[931px] outline outline-1 outline-offset-[-0.50px] outline-zinc-300" />
<div className="w-[1438.50px] h-0 outline outline-1 outline-offset-[-0.50px] outline-zinc-300" />
<div className="w-56 h-0 outline outline-1 outline-offset-[-0.50px] outline-zinc-300" />
<div className="w-52 h-4 relative">
  <div className="w-4 h-4 left-[187px] top-0 absolute">
    <div className="w-1.5 h-0 left-[5.33px] top-[8px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-0 h-1.5 left-[8px] top-[5.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-3.5 h-3.5 left-[1.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-0 top-0 absolute justify-start text-zinc-500 text-xs font-bold font-['Inter'] uppercase">my projects</div>
</div>
<div className="w-56 h-10 relative">
  <div className="w-56 h-10 left-0 top-0 absolute bg-indigo-600/10 rounded-md" />
  <div className="left-[36px] top-[10px] absolute justify-start text-slate-900 text-base font-semibold font-['Inter']">Mobile App</div>
  <div className="left-[195px] top-[6px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-2 h-2 left-[12px] top-[15px] absolute bg-lime-400 rounded-full" />
</div>
<div className="w-40 h-5 relative">
  <div className="left-[24px] top-0 absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Website Redesign</div>
  <div className="w-2 h-2 left-0 top-[5px] absolute bg-amber-500 rounded-full" />
</div>
<div className="w-36 h-5 relative">
  <div className="left-[24px] top-0 absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Design System</div>
  <div className="w-2 h-2 left-0 top-[5px] absolute bg-purple-200 rounded-full" />
</div>
<div className="w-28 h-5 relative">
  <div className="left-[24px] top-0 absolute justify-start text-zinc-500 text-base font-medium font-['Inter']">Wireframes</div>
  <div className="w-2 h-2 left-0 top-[5px] absolute bg-blue-400 rounded-full" />
</div>
<div className="w-6 h-6 relative">
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-3 h-3 left-[2px] top-[10.21px] absolute opacity-60 bg-indigo-600" />
    <div className="w-3 h-3 left-[6px] top-[2px] absolute bg-indigo-600" />
    <div className="w-2.5 h-3 left-[12px] top-[10.21px] absolute opacity-40 bg-indigo-600" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
  </div>
</div>
<div className="w-6 h-5 relative">
  <div className="w-5 h-5 left-[6px] top-0 absolute">
    <div className="w-2 h-3.5 left-[5.96px] top-[2.78px] absolute bg-zinc-500" />
    <div className="w-5 h-5 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="w-5 h-5 left-0 top-0 absolute">
    <div className="w-2 h-3.5 left-[5.96px] top-[2.78px] absolute bg-zinc-500" />
    <div className="w-5 h-5 left-0 top-0 absolute opacity-0" />
  </div>
</div>
<div className="w-24 h-6 relative">
  <div className="left-0 top-0 absolute justify-start text-slate-900 text-xl font-semibold font-['Inter']">Project M.</div>
</div>
<div className="w-96 h-[625px] bg-neutral-100 rounded-tl-2xl rounded-tr-2xl" />
<div className="w-80 h-44 relative">
  <div className="w-80 h-44 left-0 top-0 absolute bg-white rounded-2xl" />
  <div className="w-4 h-4 left-[238px] top-[137px] absolute">
    <div className="w-3.5 h-3.5 left-[1px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[5.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6px] top-[10px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[126px] top-[137px] absolute">
    <div className="w-3.5 h-3.5 left-[0.83px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.33px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[4.67px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[147px] top-[138px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">12 comments</div>
  <div className="left-[259px] top-[138px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">0 files</div>
  <img className="w-6 h-6 left-[59px] top-[133px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <img className="w-6 h-6 left-[39px] top-[133px] absolute rounded-full border border-white" src="https://placehold.co/25x24" />
  <img className="w-6 h-6 left-[20px] top-[133px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[20px] top-[47px] absolute justify-start text-slate-900 text-lg font-semibold font-['Inter']">Brainstorming</div>
  <div className="left-[278px] top-[19px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-72 left-[20px] top-[75px] absolute justify-start text-zinc-500 text-xs font-normal font-['Inter']">Brainstorming brings team members' diverse experience into play. </div>
  <div className="w-9 h-6 left-[20px] top-[20px] absolute bg-orange-300/20 rounded" />
  <div className="left-[26px] top-[24px] absolute justify-start text-orange-400 text-xs font-medium font-['Inter']">Low</div>
</div>
<div className="w-80 h-0 bg-fuchsia-800 outline outline-[3px] outline-offset-[-1.50px] outline-indigo-600" />
<div className="w-80 h-52 origin-top-left rotate-3 bg-stone-300/30 rounded-2xl blur" />
<div className="w-16 h-5 relative">
  <div className="left-[16px] top-0 absolute justify-start text-slate-900 text-base font-medium font-['Inter']">To Do</div>
  <div className="w-2 h-2 left-0 top-[5px] absolute bg-indigo-600 rounded-full" />
</div>
<div className="w-96 h-[625px] bg-neutral-100 rounded-tl-2xl rounded-tr-2xl" />
<div className="w-96 h-[625px] bg-neutral-100 rounded-tl-2xl rounded-tr-2xl" />
<div className="w-96 h-11 relative">
  <div className="w-96 h-11 left-0 top-0 absolute bg-neutral-100 rounded-md border" />
  <div className="left-[54.80px] top-[13px] absolute justify-start text-zinc-500 text-sm font-normal font-['Inter']">Search for anything...</div>
  <div className="w-5 h-5 left-[16.80px] top-[10px] absolute">
    <div className="w-4 h-4 left-[1.83px] top-[1.83px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-0.5 h-0.5 left-[18.33px] top-[18.33px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-5 h-5 left-0 top-0 absolute opacity-0" />
  </div>
</div>
<div className="w-52 h-10 relative">
  <div className="w-4 h-4 left-[183px] top-[10px] absolute">
    <div className="w-3 h-1.5 left-[2.50px] top-[6.15px] absolute bg-zinc-800" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-0 top-0 absolute text-right justify-start text-slate-900 text-base font-normal font-['Inter']">Anima Agrawal</div>
  <div className="left-[51px] top-[22px] absolute text-right justify-start text-zinc-500 text-sm font-normal font-['Inter']">U.P, India</div>
</div>
<div className="w-28 h-6 relative">
  <div className="w-6 h-6 left-[96px] top-0 absolute">
    <div className="w-4 h-4 left-[3.97px] top-[2.91px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-1 h-[1.26px] left-[10.17px] top-[1.94px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-1.5 h-[3px] left-[9.02px] top-[19.06px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
    <div className="w-1.5 h-1.5 left-[15px] top-[1px] absolute bg-red-400 rounded-full" />
  </div>
  <div className="w-6 h-6 left-[48px] top-0 absolute">
    <div className="w-5 h-5 left-[2px] top-[2.43px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-[3.32px] h-1 left-[10.34px] top-[6.86px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
    <div className="w-px h-px left-[11.50px] top-[13.25px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
  </div>
  <div className="w-6 h-6 left-0 top-0 absolute">
    <div className="w-0 h-[3px] left-[8px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-0 h-[3px] left-[16px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-4 h-0 left-[3.50px] top-[9.09px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-4 h-5 left-[3px] top-[3.50px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-zinc-500" />
    <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
    <div className="w-px h-px left-[11.50px] top-[13.20px] absolute outline outline-2 outline-offset-[-1px] outline-zinc-500" />
    <div className="w-px h-px left-[7.80px] top-[13.20px] absolute outline outline-2 outline-offset-[-1px] outline-zinc-500" />
    <div className="w-px h-px left-[7.80px] top-[16.20px] absolute outline outline-2 outline-offset-[-1px] outline-zinc-500" />
  </div>
</div>
<div className="w-64 h-10 relative">
  <div className="w-0 h-0.5 left-[154.33px] top-[13.33px] absolute outline outline-[1.30px] outline-offset-[-0.65px] outline-zinc-500" />
  <div className="w-0 h-0.5 left-[159.67px] top-[13.33px] absolute outline outline-[1.30px] outline-offset-[-0.65px] outline-zinc-500" />
  <div className="w-3 h-3 left-[151px] top-[14.33px] absolute outline outline-[1.30px] outline-offset-[-0.65px] outline-zinc-500" />
  <div className="w-3 h-0 left-[151.17px] top-[23.73px] absolute outline outline-[1.30px] outline-offset-[-0.65px] outline-zinc-500" />
  <div className="w-[3.33px] h-1 left-[155.33px] top-[17.50px] absolute bg-zinc-500" />
  <div className="w-4 h-4 left-[149px] top-[12px] absolute opacity-0" />
  <div className="w-32 h-10 left-[134px] top-0 absolute rounded-md border border-zinc-500" />
  <div className="w-4 h-4 left-[228px] top-[13px] absolute">
    <div className="w-3 h-1.5 left-[2.22px] top-[5.47px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[171px] top-[11px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter'] capitalize">Today</div>
  <div className="w-4 h-4 left-[15px] top-[12px] absolute">
    <div className="w-3 h-3.5 left-[2.27px] top-[1px] absolute outline outline-[1.30px] outline-offset-[-0.65px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="w-4 h-4 left-[86px] top-[12px] absolute">
    <div className="w-3 h-1.5 left-[2.22px] top-[5.47px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="w-32 h-10 left-0 top-0 absolute rounded-md border border-zinc-500" />
  <div className="left-[37px] top-[11px] absolute justify-start text-zinc-500 text-base font-medium font-['Inter'] capitalize">Filter</div>
</div>
<div className="w-80 h-14 relative">
  <div className="w-7 h-7 left-[272px] top-[16px] absolute">
    <div className="w-6 h-6 left-[2.50px] top-[2.50px] absolute bg-indigo-600/20 outline outline-[1.50px] outline-offset-[-0.75px]" />
    <div className="w-3 h-3 left-[8.75px] top-[8.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-indigo-600" />
    <div className="w-1 h-1 left-[15.21px] top-[10.73px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-indigo-600" />
    <div className="w-7 h-7 left-[30px] top-[30px] absolute origin-top-left -rotate-180 opacity-0" />
  </div>
  <div className="w-7 h-7 left-[314px] top-[16px] absolute">
    <div className="w-6 h-6 left-[2.50px] top-[2.50px] absolute bg-indigo-600/20 outline outline-[1.50px] outline-offset-[-0.75px]" />
    <div className="w-7 h-7 left-[30px] top-[30px] absolute origin-top-left -rotate-180 opacity-0" />
  </div>
  <div className="w-4 h-4 left-[321px] top-[23px] absolute">
    <div className="w-1.5 h-2 left-[9.49px] top-[3.83px] absolute bg-indigo-600 outline outline-[0.30px] outline-indigo-600" />
    <div className="w-1.5 h-2 left-[0.83px] top-[3.83px] absolute bg-indigo-600 outline outline-[0.30px] outline-indigo-600" />
    <div className="w-1.5 h-px left-[4.83px] top-[7.50px] absolute bg-indigo-600 outline outline-[0.30px] outline-indigo-600" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-0 top-0 absolute justify-start text-slate-900 text-5xl font-semibold font-['Inter'] capitalize">Mobile App</div>
</div>
<div className="w-5 h-5 relative rounded-[10px]">
  <div className="w-5 h-5 left-0 top-0 absolute bg-neutral-200 rounded-[10px] border" />
  <div className="left-[6px] top-[2px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">4</div>
</div>
<div className="w-5 h-5 relative rounded-[10px]">
  <div className="w-5 h-5 left-0 top-0 absolute bg-neutral-200 rounded-[10px] border" />
  <div className="left-[6px] top-[2px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">3</div>
</div>
<div className="w-5 h-5 relative rounded-[10px]">
  <div className="w-5 h-5 left-0 top-0 absolute bg-neutral-200 rounded-[10px] border" />
  <div className="left-[6px] top-[2px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">2</div>
</div>
<div className="w-8 h-8 bg-amber-300/70 rounded-full blur-lg" />
<div className="w-80 h-0 outline outline-[3px] outline-offset-[-1.50px] outline-amber-500" />
<div className="w-28 h-5 relative">
  <div className="left-[16px] top-0 absolute justify-start text-slate-900 text-base font-medium font-['Inter']">On Progress</div>
  <div className="w-2 h-2 left-0 top-[5px] absolute bg-amber-500 rounded-full" />
</div>
<div className="w-80 h-0 outline outline-[3px] outline-offset-[-1.50px] outline-neutral-400" />
<div className="w-14 h-5 relative">
  <div className="left-[16px] top-0 absolute justify-start text-slate-900 text-base font-medium font-['Inter']">Done</div>
  <div className="w-2 h-2 left-0 top-[5px] absolute bg-blue-400 rounded-full" />
</div>
<div className="w-80 h-44 bg-indigo-600/5 rounded-[10px] border border-indigo-600/60" />
<div className="w-80 h-44 relative">
  <div className="w-80 h-44 left-0 top-0 absolute bg-white rounded-2xl" />
  <div className="w-4 h-4 left-[232px] top-[137px] absolute">
    <div className="w-3.5 h-3.5 left-[1px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[5.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6px] top-[10px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[120px] top-[137px] absolute">
    <div className="w-3.5 h-3.5 left-[0.83px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.33px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[4.67px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[141px] top-[138px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">12 comments</div>
  <div className="left-[253px] top-[138px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">15 files</div>
  <img className="w-6 h-6 left-[59px] top-[133px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <img className="w-6 h-6 left-[39px] top-[133px] absolute rounded-full border border-white" src="https://placehold.co/25x24" />
  <img className="w-6 h-6 left-[20px] top-[133px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[20px] top-[47px] absolute justify-start text-slate-900 text-lg font-semibold font-['Inter']">Design System</div>
  <div className="left-[278px] top-[19px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-72 left-[20px] top-[75px] absolute justify-start text-slate-900 text-xs font-normal font-['Inter']">It just needs to adapt the UI from what you did before </div>
  <div className="w-20 h-6 left-[20px] top-[20px] absolute bg-gray-400/20 rounded" />
  <div className="left-[26px] top-[24px] absolute justify-start text-green-400 text-xs font-medium font-['Inter']">Completed</div>
</div>
<div className="w-80 h-56 relative">
  <div className="w-80 h-56 left-0 top-0 absolute bg-white rounded-2xl" />
  <img className="w-32 h-20 left-[20px] top-[76px] absolute rounded-lg" src="https://placehold.co/131x79" />
  <img className="w-32 h-20 left-[163px] top-[76px] absolute rounded-lg" src="https://placehold.co/131x79" />
  <div className="w-4 h-4 left-[232px] top-[187px] absolute">
    <div className="w-3.5 h-3.5 left-[1px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[5.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6px] top-[10px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[125px] top-[187px] absolute">
    <div className="w-3.5 h-3.5 left-[0.83px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.33px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[4.67px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[146px] top-[188px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">9 comments</div>
  <div className="left-[253px] top-[188px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">10 files</div>
  <img className="w-6 h-6 left-[20px] top-[183px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[20px] top-[47px] absolute justify-start text-slate-900 text-lg font-semibold font-['Inter']">Moodboard</div>
  <div className="left-[278px] top-[19px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-9 h-6 left-[20px] top-[20px] absolute bg-orange-300/20 rounded" />
  <div className="left-[26px] top-[24px] absolute justify-start text-orange-400 text-xs font-medium font-['Inter']">Low</div>
</div>
<div className="w-80 h-64 relative">
  <div className="w-80 h-64 left-0 top-0 absolute bg-white rounded-2xl" />
  <img className="w-72 h-28 left-[20px] top-[76px] absolute rounded-lg" src="https://placehold.co/282x110" />
  <div className="w-4 h-4 left-[232px] top-[218px] absolute">
    <div className="w-3.5 h-3.5 left-[1px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[5.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6px] top-[10px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[120px] top-[218px] absolute">
    <div className="w-3.5 h-3.5 left-[0.83px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.33px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[4.67px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[141px] top-[219px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">14 comments</div>
  <div className="left-[253px] top-[219px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">15 files</div>
  <img className="w-6 h-6 left-[59px] top-[214px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <img className="w-6 h-6 left-[39px] top-[214px] absolute rounded-full border border-white" src="https://placehold.co/25x24" />
  <img className="w-6 h-6 left-[20px] top-[214px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[20px] top-[47px] absolute justify-start text-slate-900 text-lg font-semibold font-['Inter']">Onboarding Illustrations </div>
  <div className="left-[278px] top-[19px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-9 h-6 left-[20px] top-[20px] absolute bg-orange-300/20 rounded" />
  <div className="left-[26px] top-[24px] absolute justify-start text-orange-400 text-xs font-medium font-['Inter']">Low</div>
</div>
<div className="w-80 h-80 relative">
  <div className="w-80 h-80 left-0 top-0 absolute bg-white rounded-2xl" />
  <div className="w-72 h-44 left-[20px] top-[76px] absolute bg-stone-300 rounded-lg" />
  <div className="w-72 h-44 left-[20px] top-[76px] absolute bg-stone-300 rounded-lg" />
  <img className="w-72 h-56 left-[5px] top-[76px] absolute" src="https://placehold.co/304x228" />
  <div className="w-4 h-4 left-[232px] top-[288px] absolute">
    <div className="w-3.5 h-3.5 left-[1px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[5.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6px] top-[10px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[120px] top-[288px] absolute">
    <div className="w-3.5 h-3.5 left-[0.83px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.33px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[4.67px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[141px] top-[289px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">12 comments</div>
  <div className="left-[253px] top-[289px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">15 files</div>
  <img className="w-6 h-6 left-[39px] top-[284px] absolute rounded-full border border-white" src="https://placehold.co/25x24" />
  <img className="w-6 h-6 left-[20px] top-[284px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[20px] top-[47px] absolute justify-start text-slate-900 text-lg font-semibold font-['Inter']">Mobile App Design</div>
  <div className="left-[278px] top-[19px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-20 h-6 left-[20px] top-[20px] absolute bg-gray-400/20 rounded" />
  <div className="left-[26px] top-[24px] absolute justify-start text-green-400 text-xs font-medium font-['Inter']">Completed</div>
</div>
<div className="w-60 h-9 relative">
  <div className="left-[25px] top-[10px] absolute justify-start text-indigo-600 text-base font-medium font-['Inter'] capitalize">Invite</div>
  <div className="w-4 h-4 left-0 top-[11px] absolute">
    <div className="w-1.5 h-0 left-[6px] top-[9px] absolute outline outline-1 outline-offset-[-0.50px] outline-indigo-600" />
    <div className="w-0 h-1.5 left-[9px] top-[6px] absolute outline outline-1 outline-offset-[-0.50px] outline-indigo-600" />
    <div className="w-3.5 h-3.5 left-[1.50px] top-[1.50px] absolute bg-indigo-600/20 outline outline-[1.50px] outline-offset-[-0.75px]" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <img className="w-9 h-9 left-[79px] top-0 absolute rounded-full border border-white" src="https://placehold.co/38x38" />
  <img className="w-9 h-9 left-[109px] top-0 absolute rounded-full border border-white" src="https://placehold.co/38x38" />
  <img className="w-9 h-9 left-[139px] top-0 absolute rounded-full border border-white" src="https://placehold.co/38x38" />
  <img className="w-9 h-9 left-[169px] top-0 absolute rounded-full border border-white" src="https://placehold.co/38x38" />
  <div className="w-9 h-9 left-[199px] top-0 absolute bg-rose-200 rounded-full border border-white" />
  <div className="left-[208px] top-[10px] absolute justify-start text-red-400 text-base font-medium font-['Inter'] capitalize">+2</div>
</div>
<div className="w-96 h-36 relative">
  <div className="w-96 h-36 left-0 top-0 absolute bg-stone-300" />
  <div className="w-80 h-44 left-[20px] top-[7px] absolute bg-white rounded-2xl" />
  <div className="w-4 h-4 left-[258px] top-[144px] absolute">
    <div className="w-3.5 h-3.5 left-[1px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[5.33px] top-[1.33px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6px] top-[10px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[152px] top-[144px] absolute">
    <div className="w-3.5 h-3.5 left-[0.83px] top-[0.83px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.33px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[4.67px] top-[6.67px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-0 top-0 absolute opacity-0" />
  </div>
  <div className="left-[173px] top-[145px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">2 comments</div>
  <div className="left-[279px] top-[145px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">0 files</div>
  <img className="w-6 h-6 left-[79px] top-[140px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <img className="w-6 h-6 left-[59px] top-[140px] absolute rounded-full border border-white" src="https://placehold.co/25x24" />
  <img className="w-6 h-6 left-[40px] top-[140px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[40px] top-[54px] absolute justify-start text-slate-900 text-lg font-semibold font-['Inter']">Wireframes</div>
  <div className="left-[298px] top-[26px] absolute justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-72 left-[40px] top-[82px] absolute justify-start text-zinc-500 text-xs font-normal font-['Inter']">Low fidelity wireframes include the most basic content and visuals.</div>
  <div className="w-10 h-6 left-[40px] top-[27px] absolute bg-red-400/10 rounded" />
  <div className="left-[46px] top-[31px] absolute justify-start text-red-400 text-xs font-medium font-['Inter']">High</div>
</div>
<div className="w-80 h-6 relative">
  <div className="w-80 h-6 left-0 top-0 absolute bg-stone-300" />
  <div className="w-80 h-56 left-[14px] top-[14px] absolute bg-white rounded-2xl" />
</div>
<div className="w-40 h-10 bg-white rounded" />
<div className="justify-start text-black text-sm font-medium font-['Inter']">Write a message</div>
<div className="w-6 h-6 relative">
  <div className="w-4 h-4 left-[3.97px] top-[2px] absolute bg-yellow-400" />
  <div className="w-2 h-0.5 left-[8.05px] top-[20.15px] absolute bg-yellow-400" />
  <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
</div>
<div className="justify-start text-black text-sm font-medium font-['Inter']">Thoughts Time</div>
<div className="w-40 text-center justify-start text-zinc-500 text-xs font-normal font-['Inter']">We don’t have any notice for you, till then you can share your thoughts with your peers.</div>
<div className="w-6 h-6 relative">
  <div className="w-5 h-5 left-[2px] top-[2px] absolute opacity-20 bg-indigo-600" />
  <div className="w-2.5 h-2.5 left-[7.25px] top-[7.25px] absolute bg-indigo-600" />
  <div className="w-6 h-6 left-0 top-0 absolute opacity-0" />
</div>
<div className="w-80 h-48 relative">
  <div className="w-80 h-44 left-[9.26px] top-0 absolute origin-top-left rotate-3 bg-white rounded-2xl shadow-[0px_3px_14px_4px_rgba(80,48,229,0.05)]" />
  <div className="w-4 h-4 left-[239.77px] top-[149.27px] absolute">
    <div className="w-3.5 h-3.5 left-[1.77px] top-[1.38px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-2 h-[2.92px] left-[6.09px] top-[1.61px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
    <div className="w-4 h-4 left-[0.84px] top-0 absolute opacity-0" />
    <div className="w-[3.33px] h-0 left-[6.31px] top-[10.30px] absolute outline outline-1 outline-offset-[-0.50px] outline-zinc-500" />
  </div>
  <div className="w-4 h-4 left-[127.92px] top-[143.41px] absolute">
    <div className="w-3.5 h-3.5 left-[1.63px] top-[0.88px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[7.81px] top-[7.04px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[10.47px] top-[7.18px] absolute bg-zinc-500" />
    <div className="w-[1.33px] h-[1.33px] left-[5.15px] top-[6.90px] absolute bg-zinc-500" />
    <div className="w-4 h-4 left-[0.84px] top-0 absolute opacity-0" />
  </div>
  <div className="left-[148.84px] top-[145.50px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">10 comments</div>
  <div className="left-[260.69px] top-[151.37px] absolute justify-start text-zinc-500 text-xs font-medium font-['Inter']">3 files</div>
  <img className="w-6 h-6 left-[41.25px] top-[134.86px] absolute rounded-full border border-white" src="https://placehold.co/25x24" />
  <img className="w-6 h-6 left-[22.28px] top-[133.86px] absolute rounded-full border border-white" src="https://placehold.co/24x24" />
  <div className="left-[26.78px] top-[47.98px] absolute origin-top-left rotate-3 justify-start text-slate-900 text-lg font-semibold font-['Inter']">Research</div>
  <div className="left-[285.89px] top-[33.52px] absolute origin-top-left rotate-3 justify-start text-slate-900 text-base font-extrabold font-['Inter']">. . .</div>
  <div className="w-72 left-[25.31px] top-[75.94px] absolute origin-top-left rotate-3 justify-start text-zinc-500 text-xs font-normal font-['Inter']">User research helps you to create an optimal product for users.</div>
  <div className="w-10 h-6 left-[28.19px] top-[21.02px] absolute bg-red-400/10 rounded" />
  <div className="left-[33.97px] top-[25.33px] absolute justify-start text-red-400 text-xs font-medium font-['Inter']">High</div>
</div>
<div className="w-6 h-6 relative overflow-hidden">
  <div className="w-3 h-3 left-[5.43px] top-[6.37px] absolute bg-white" />
  <div className="w-3 h-3 left-[5.43px] top-[6.37px] absolute outline outline-[0.75px] outline-offset-[-0.38px] outline-black" />
  <div className="w-[0.75px] h-1 left-[15px] top-[11px] absolute bg-black" />
  <div className="w-[0.77px] h-1 left-[13px] top-[11px] absolute bg-black" />
  <div className="w-[0.77px] h-1 left-[11px] top-[11px] absolute bg-black" />
</div>
<div className="w-9 h-9 relative">
  <div className="w-9 h-9 left-0 top-0 absolute bg-black rounded-[19px]" />
  <img className="w-12 h-16 left-[-4px] top-[-5px] absolute" src="https://placehold.co/47x71" />
</div>