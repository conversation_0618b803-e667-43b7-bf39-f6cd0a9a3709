<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Rave - Dropshipping Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette - Enhanced */
            --primary: #5030e5;
            --primary-50: #f0f4ff;
            --primary-100: #e0e7ff;
            --primary-200: #c7d2fe;
            --primary-300: #a5b4fc;
            --primary-400: #818cf8;
            --primary-500: #6366f1;
            --primary-600: #5030e5;
            --primary-700: #4338ca;
            --primary-800: #3730a3;
            --primary-900: #312e81;
            --primary-light: #6366f1;
            --primary-dark: #4338ca;
            --primary-bg: rgba(80, 48, 229, 0.08);
            --primary-hover: rgba(80, 48, 229, 0.12);

            /* Neutral Colors - Enhanced */
            --white: #ffffff;
            --gray-25: #fcfcfd;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --gray-950: #030712;

            /* Status Colors - Enhanced */
            --success: #10b981;
            --success-50: #ecfdf5;
            --success-100: #d1fae5;
            --success-200: #a7f3d0;
            --success-500: #10b981;
            --success-600: #059669;
            --success-700: #047857;
            --success-bg: rgba(16, 185, 129, 0.1);
            --success-border: rgba(16, 185, 129, 0.2);

            --warning: #f59e0b;
            --warning-50: #fffbeb;
            --warning-100: #fef3c7;
            --warning-200: #fde68a;
            --warning-500: #f59e0b;
            --warning-600: #d97706;
            --warning-700: #b45309;
            --warning-bg: rgba(245, 158, 11, 0.1);
            --warning-border: rgba(245, 158, 11, 0.2);

            --danger: #ef4444;
            --danger-50: #fef2f2;
            --danger-100: #fee2e2;
            --danger-200: #fecaca;
            --danger-500: #ef4444;
            --danger-600: #dc2626;
            --danger-700: #b91c1c;
            --danger-bg: rgba(239, 68, 68, 0.1);
            --danger-border: rgba(239, 68, 68, 0.2);

            --info: #3b82f6;
            --info-50: #eff6ff;
            --info-100: #dbeafe;
            --info-200: #bfdbfe;
            --info-500: #3b82f6;
            --info-600: #2563eb;
            --info-700: #1d4ed8;
            --info-bg: rgba(59, 130, 246, 0.1);
            --info-border: rgba(59, 130, 246, 0.2);

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-12: 3rem;

            /* Border Radius */
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 16px;
            --radius-xl: 30px;

            /* Enhanced Shadow System */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

            /* Colored Shadows */
            --shadow-primary: 0 4px 14px 0 rgba(80, 48, 229, 0.15);
            --shadow-success: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
            --shadow-warning: 0 4px 14px 0 rgba(245, 158, 11, 0.15);
            --shadow-danger: 0 4px 14px 0 rgba(239, 68, 68, 0.15);

            /* Glow Effects */
            --glow-primary: 0 0 20px rgba(80, 48, 229, 0.3);
            --glow-success: 0 0 20px rgba(16, 185, 129, 0.3);
            --glow-warning: 0 0 20px rgba(245, 158, 11, 0.3);
            --glow-danger: 0 0 20px rgba(239, 68, 68, 0.3);

            /* Typography */
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;

            /* Typography Scale */
            --text-xs: 0.75rem;      /* 12px */
            --text-sm: 0.875rem;     /* 14px */
            --text-base: 1rem;       /* 16px */
            --text-lg: 1.125rem;     /* 18px */
            --text-xl: 1.25rem;      /* 20px */
            --text-2xl: 1.5rem;      /* 24px */
            --text-3xl: 1.875rem;    /* 30px */
            --text-4xl: 2.25rem;     /* 36px */
            --text-5xl: 3rem;        /* 48px */

            /* Line Heights */
            --leading-tight: 1.25;
            --leading-normal: 1.5;
            --leading-relaxed: 1.75;

            /* Font Weights */
            --font-light: 300;
            --font-normal: 400;
            --font-medium: 500;
            --font-semibold: 600;
            --font-bold: 700;
            --font-extrabold: 800;
            --font-black: 900;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background:
                radial-gradient(circle at 20% 80%, rgba(80, 48, 229, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, var(--gray-25) 0%, var(--gray-50) 100%);
            color: var(--gray-900);
            line-height: var(--leading-normal);
            font-size: var(--text-sm);
            font-weight: var(--font-normal);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
        }

        /* Modern Layout Container */
        .app-container {
            display: flex;
            min-height: calc(100vh - 48px);
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
            border-radius: var(--radius-xl);
            margin: 24px;
            box-shadow:
                0 0 0 1px rgba(255, 255, 255, 0.2),
                var(--shadow-2xl),
                0 0 60px rgba(80, 48, 229, 0.08);
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            z-index: 1;
        }

        .app-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(80, 48, 229, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 300px;
            background:
                linear-gradient(180deg,
                    rgba(255, 255, 255, 0.8) 0%,
                    rgba(248, 250, 252, 0.6) 100%);
            border-right: 1px solid rgba(229, 231, 235, 0.6);
            display: flex;
            flex-direction: column;
            padding: var(--space-8) 0;
            position: relative;
            backdrop-filter: blur(10px);
            z-index: 2;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 50% 0%, rgba(80, 48, 229, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        .sidebar::after {
            content: '';
            position: absolute;
            top: var(--space-8);
            right: 0;
            bottom: var(--space-8);
            width: 1px;
            background: linear-gradient(180deg, transparent, rgba(229, 231, 235, 0.8), transparent);
        }

        .sidebar-header {
            padding: 0 var(--space-8) var(--space-8);
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: var(--space-8);
            position: relative;
        }

        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: var(--space-8);
            right: var(--space-8);
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
        }

        .logo {
            font-size: var(--text-2xl);
            font-weight: var(--font-black);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            letter-spacing: 0.1em;
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .logo::before {
            content: '⚡';
            font-size: var(--text-3xl);
            -webkit-text-fill-color: var(--primary);
        }

        .nav-menu {
            flex: 1;
            padding: 0 var(--space-6);
        }

        .nav-section {
            margin-bottom: var(--space-8);
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 700;
            color: var(--gray-500);
            text-transform: uppercase;
            margin-bottom: var(--space-4);
            padding: 0 var(--space-4);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-4) var(--space-6);
            color: var(--gray-600);
            text-decoration: none;
            border-radius: var(--radius-lg);
            margin: 0 var(--space-4) var(--space-2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: var(--font-medium);
            font-size: var(--text-sm);
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-bg) 0%, rgba(80, 48, 229, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-item:hover {
            background: var(--gray-100);
            color: var(--gray-900);
            transform: translateX(4px);
        }

        .nav-item:hover::before {
            opacity: 1;
        }

        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-bg) 0%, rgba(80, 48, 229, 0.12) 100%);
            color: var(--primary);
            font-weight: var(--font-semibold);
            box-shadow:
                0 1px 3px rgba(80, 48, 229, 0.1),
                0 4px 12px rgba(80, 48, 229, 0.15);
            border: 1px solid rgba(80, 48, 229, 0.2);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(180deg, var(--primary) 0%, var(--primary-light) 100%);
            border-radius: 0 2px 2px 0;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-lg);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .nav-item:hover .nav-icon {
            transform: scale(1.1);
        }

        .nav-item.active .nav-icon {
            transform: scale(1.15);
            filter: drop-shadow(0 2px 4px rgba(80, 48, 229, 0.3));
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--gray-50);
        }

        /* Header */
        .header {
            background: linear-gradient(180deg, var(--white) 0%, rgba(248, 250, 252, 0.8) 100%);
            padding: var(--space-8) var(--space-8);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(8px);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: var(--space-8);
            right: var(--space-8);
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-6);
        }

        .header-title {
            font-size: var(--text-4xl);
            font-weight: var(--font-bold);
            color: var(--gray-900);
            text-transform: capitalize;
            letter-spacing: -0.025em;
            line-height: var(--leading-tight);
            background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .search-container {
            position: relative;
            width: 480px;
            max-width: 100%;
        }

        .search-input {
            width: 100%;
            padding: var(--space-4) var(--space-6) var(--space-4) 3.5rem;
            background: var(--white);
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--gray-900);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 1px 3px rgba(0, 0, 0, 0.1),
                0 0 0 0 rgba(80, 48, 229, 0);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow:
                0 1px 3px rgba(0, 0, 0, 0.1),
                0 0 0 4px rgba(80, 48, 229, 0.1);
            background: var(--white);
        }

        .search-input::placeholder {
            color: var(--gray-500);
            font-weight: var(--font-normal);
        }

        .search-icon {
            position: absolute;
            left: var(--space-4);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: var(--text-lg);
            transition: color 0.3s ease;
        }

        .search-container:focus-within .search-icon {
            color: var(--primary);
        }

        .action-btn {
            padding: var(--space-3) var(--space-6);
            border: 1px solid rgba(107, 114, 128, 0.3);
            border-radius: var(--radius-lg);
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(255, 255, 255, 0.7) 100%);
            color: var(--gray-700);
            font-weight: var(--font-medium);
            font-size: var(--text-sm);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:hover {
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 1) 0%,
                    rgba(248, 250, 252, 0.9) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: rgba(107, 114, 128, 0.4);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-600) 100%);
            color: var(--white);
            border-color: var(--primary-700);
            box-shadow: var(--shadow-primary);
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl), var(--glow-primary);
        }

        /* User Profile */
        .user-profile {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(255, 255, 255, 0.7) 100%);
            padding: var(--space-3) var(--space-4);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .user-profile:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .user-avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: var(--shadow-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-weight: var(--font-bold);
            font-size: var(--text-lg);
            position: relative;
            overflow: hidden;
        }

        .user-avatar::before {
            content: '👤';
            font-size: var(--text-lg);
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .user-info {
            text-align: right;
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
        }

        .user-name {
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            line-height: var(--leading-tight);
        }

        .user-location {
            font-size: var(--text-sm);
            color: var(--gray-500);
            font-weight: var(--font-medium);
        }

        /* Content Area */
        .content-area {
            flex: 1;
            padding: var(--space-8);
            overflow-y: auto;
        }

        /* Stats Cards */
        .stats-section {
            margin-bottom: var(--space-8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-6);
        }

        .stat-card {
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(255, 255, 255, 0.7) 100%);
            padding: var(--space-8);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl), var(--shadow-primary);
        }

        .stat-card:nth-child(2)::before {
            background: linear-gradient(90deg, var(--success) 0%, var(--success-600) 100%);
        }

        .stat-card:nth-child(3)::before {
            background: linear-gradient(90deg, var(--warning) 0%, var(--warning-600) 100%);
        }

        .stat-value {
            font-size: var(--text-4xl);
            font-weight: var(--font-extrabold);
            background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-2);
            line-height: var(--leading-tight);
        }

        .stat-label {
            font-size: var(--text-sm);
            color: var(--gray-600);
            font-weight: var(--font-medium);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-top: var(--space-6);
        }

        /* Product Cards */
        .product-card {
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(255, 255, 255, 0.85) 100%);
            border-radius: var(--radius-lg);
            padding: var(--space-8);
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 50% 0%, rgba(80, 48, 229, 0.02) 0%, transparent 50%);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl), var(--glow-primary);
            border-color: rgba(80, 48, 229, 0.2);
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card.selected {
            border-color: var(--primary);
            box-shadow:
                var(--shadow-xl),
                0 0 0 4px rgba(80, 48, 229, 0.1),
                var(--glow-primary);
            transform: translateY(-4px);
        }

        .product-card.selected::before {
            opacity: 1;
            background:
                radial-gradient(circle at 50% 0%, rgba(80, 48, 229, 0.05) 0%, transparent 50%);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-6);
            position: relative;
        }

        .product-header::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
        }

        .product-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            line-height: var(--leading-tight);
            margin-bottom: var(--space-4);
            flex: 1;
            margin-right: var(--space-4);
        }

        .product-price {
            background: linear-gradient(135deg, var(--success-100) 0%, var(--success-50) 100%);
            color: var(--success-700);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-xl);
            font-weight: var(--font-bold);
            font-size: var(--text-sm);
            border: 1px solid var(--success-200);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .product-price::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        .product-images {
            margin: var(--space-6) 0;
            position: relative;
        }

        .main-image {
            width: 100%;
            height: 240px;
            object-fit: cover;
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-4);
            box-shadow: var(--shadow-lg);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .main-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .main-image:hover::before {
            opacity: 1;
        }

        .main-image:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-2xl);
        }

        .gallery-images {
            display: flex;
            gap: var(--space-3);
            overflow-x: auto;
            padding: var(--space-3) 0;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-300) transparent;
        }

        .gallery-images::-webkit-scrollbar {
            height: 4px;
        }

        .gallery-images::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: 2px;
        }

        .gallery-images::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 2px;
        }

        .gallery-images::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        .gallery-image {
            width: 72px;
            height: 72px;
            object-fit: cover;
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
            border: 2px solid rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
        }

        .gallery-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(80, 48, 229, 0.2) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-image:hover {
            transform: scale(1.15) rotate(2deg);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary);
        }

        .gallery-image:hover::before {
            opacity: 1;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--space-6);
            padding-top: var(--space-6);
            border-top: 1px solid rgba(229, 231, 235, 0.6);
            font-size: var(--text-sm);
            color: var(--gray-600);
            position: relative;
        }

        .product-meta::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
        }

        .order-info {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
        }

        .order-info span {
            font-weight: var(--font-medium);
            color: var(--gray-700);
        }

        .order-info small {
            font-size: var(--text-xs);
            color: var(--gray-500);
            font-weight: var(--font-normal);
        }

        .image-count {
            background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-50) 100%);
            color: var(--primary-700);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-xl);
            font-weight: var(--font-bold);
            font-size: var(--text-xs);
            border: 1px solid var(--primary-200);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .image-count::before {
            content: '📸';
            font-size: var(--text-sm);
        }

        .image-count::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        /* Controls Section */
        .controls-section {
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.95) 0%,
                    rgba(255, 255, 255, 0.85) 100%);
            border-radius: var(--radius-lg);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .controls-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--success) 50%, var(--warning) 100%);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--space-6);
        }

        .controls-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.025em;
        }

        .bulk-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            flex-wrap: wrap;
        }

        .selected-count {
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            color: var(--gray-600);
            background: var(--gray-100);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            border: 1px solid var(--gray-200);
        }

        .select-all-btn, .bulk-add-btn {
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-lg);
            font-weight: var(--font-semibold);
            font-size: var(--text-sm);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .select-all-btn::before, .bulk-add-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .select-all-btn:hover::before, .bulk-add-btn:hover::before {
            left: 100%;
        }

        .select-all-btn {
            border: 2px solid var(--primary);
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(255, 255, 255, 0.7) 100%);
            color: var(--primary);
            backdrop-filter: blur(10px);
        }

        .bulk-add-btn {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-600) 100%);
            color: var(--white);
            border: 2px solid var(--primary-700);
            box-shadow: var(--shadow-primary);
        }

        .select-all-btn:hover {
            background:
                linear-gradient(135deg,
                    var(--primary-50) 0%,
                    rgba(80, 48, 229, 0.1) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .bulk-add-btn:hover {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl), var(--glow-primary);
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: var(--space-12);
            color: var(--gray-600);
            font-size: var(--text-lg);
            font-weight: var(--font-medium);
            position: relative;
        }

        .loading::before {
            content: '⚡';
            font-size: var(--text-4xl);
            display: block;
            margin-bottom: var(--space-4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--space-3);
            margin-top: var(--space-12);
            padding: var(--space-6);
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(255, 255, 255, 0.7) 100%);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: var(--shadow-lg);
        }

        .pagination button {
            padding: var(--space-3) var(--space-4);
            border: 2px solid var(--gray-300);
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 0.9) 0%,
                    rgba(255, 255, 255, 0.7) 100%);
            color: var(--gray-700);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: var(--font-medium);
            font-size: var(--text-sm);
            min-width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .pagination button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .pagination button:hover::before {
            left: 100%;
        }

        .pagination button:hover {
            background:
                linear-gradient(135deg,
                    rgba(255, 255, 255, 1) 0%,
                    rgba(248, 250, 252, 0.9) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--gray-400);
        }

        .pagination button.active {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-600) 100%);
            color: var(--white);
            border-color: var(--primary-700);
            box-shadow: var(--shadow-primary);
            transform: translateY(-2px);
        }

        .pagination button.active:hover {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            box-shadow: var(--shadow-xl), var(--glow-primary);
        }

        /* Responsive Design */
        /* Enhanced Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--space-8);
            margin-top: var(--space-8);
            padding: var(--space-4);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: var(--space-6);
            }
        }

        @media (max-width: 1024px) {
            .app-container {
                margin: 16px;
                border-radius: var(--radius-lg);
            }

            .sidebar {
                width: 260px;
            }

            .header-title {
                font-size: var(--text-3xl);
            }

            .search-container {
                width: 360px;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: var(--space-6);
            }
        }

        @media (max-width: 768px) {
            .app-container {
                margin: 8px;
                border-radius: var(--radius-lg);
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid rgba(229, 231, 235, 0.6);
                padding: var(--space-6) 0;
            }

            .nav-menu {
                padding: 0 var(--space-4);
            }

            .nav-item {
                margin: 0 0 var(--space-2);
                padding: var(--space-3) var(--space-4);
            }

            .header {
                flex-direction: column;
                gap: var(--space-6);
                align-items: stretch;
                padding: var(--space-6);
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: space-between;
                gap: var(--space-4);
            }

            .search-container {
                width: 100%;
                order: -1;
            }

            .user-profile {
                order: 2;
                justify-content: center;
            }

            .products-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
                padding: var(--space-2);
            }

            .controls-section {
                padding: var(--space-6);
            }

            .controls-header {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-4);
            }

            .bulk-actions {
                justify-content: center;
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .app-container {
                margin: 4px;
                border-radius: var(--radius-md);
            }

            .header-title {
                font-size: var(--text-2xl);
            }

            .stat-card {
                padding: var(--space-6);
            }

            .stat-value {
                font-size: var(--text-3xl);
            }

            .product-card {
                padding: var(--space-6);
            }

            .main-image {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="#" class="logo">R.A.V.E</a>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <a href="index.html" class="nav-item active">
                        <div class="nav-icon">🏠</div>
                        <span>Dashboard</span>
                    </a>
                    <a href="upload.html" class="nav-item">
                        <div class="nav-icon">📋</div>
                        <span>Upload Data</span>
                    </a>
                    <a href="workspace.html" class="nav-item">
                        <div class="nav-icon">💬</div>
                        <span>Shopify Workspace</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Dashboard</h1>
                    <button class="action-btn primary">
                        <span>📊</span>
                    </button>
                </div>

                <div class="header-actions">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search products by title, order ID, or price..." id="search-input">
                        <div class="search-icon">🔍</div>
                    </div>

                    <div class="user-profile">
                        <div class="user-info">
                            <div class="user-name">Shop Rave</div>
                            <div class="user-location">Dropshipping Dashboard</div>
                        </div>
                        <div class="user-avatar"></div>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Stats Section -->
                <section class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="total-orders">0</div>
                            <div class="stat-label">Total Orders</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="selected-products">0</div>
                            <div class="stat-label">Selected</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="ready-products">0</div>
                            <div class="stat-label">Shopify Ready</div>
                        </div>
                    </div>
                </section>

                <!-- Controls Section -->
                <section class="controls-section">
                    <div class="controls-header">
                        <h2 class="controls-title">Product Selection</h2>
                        <div class="bulk-actions">
                            <span class="selected-count" id="selected-count">0 selected</span>
                            <button class="select-all-btn" id="select-all-btn">Select All</button>
                            <button class="bulk-add-btn" id="bulk-add-btn">Extract Images & Prepare for Shopify</button>
                        </div>
                    </div>
                </section>

                <!-- Products Display -->
                <div class="loading" id="loading">Loading products...</div>
                <div class="products-grid" id="products-grid" style="display: none;"></div>
                <div class="pagination" id="pagination" style="display: none;"></div>
            </div>
        </main>
    </div>

    <script src="assets/js/app.js"></script>
    <script src="assets/js/dashboard-main.js"></script>
</body>
</html>
