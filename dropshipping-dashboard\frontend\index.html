<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Rave - Dropshipping Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette inspired by the design */
            --primary: #5030e5;
            --primary-light: #6366f1;
            --primary-dark: #4338ca;
            --primary-bg: rgba(80, 48, 229, 0.08);
            --primary-hover: rgba(80, 48, 229, 0.12);

            /* Neutral Colors */
            --white: #ffffff;
            --gray-50: #f5f5f5;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #787486;
            --gray-600: #625f6d;
            --gray-700: #4b5563;
            --gray-800: #374151;
            --gray-900: #0d062d;

            /* Status Colors */
            --success: #68b266;
            --success-bg: rgba(131, 194, 157, 0.20);
            --warning: #d58d49;
            --warning-bg: rgba(223, 168, 116, 0.20);
            --danger: #d8727d;
            --danger-bg: rgba(216, 114, 125, 0.10);
            --info: #76a5ea;
            --info-bg: rgba(118, 165, 234, 0.20);

            /* Additional Colors */
            --green: #7ac555;
            --orange: #ffa500;
            --purple: #e4ccfd;
            --blue: #76a5ea;
            --red: #d25b68;

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-12: 3rem;

            /* Border Radius */
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 16px;
            --radius-xl: 30px;

            /* Shadows */
            --shadow-sm: 0px 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0px 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-lg: 0px 44px 84px 6px rgba(216, 217, 219, 1.00);
            --shadow-card: 0px 3px 14px 4px rgba(80, 48, 229, 0.05);

            /* Typography */
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.6;
            font-size: 14px;
        }

        /* Modern Layout Container */
        .app-container {
            display: flex;
            min-height: 100vh;
            background: var(--white);
            border-radius: var(--radius-xl);
            margin: 20px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 280px;
            background: var(--white);
            border-right: 1px solid var(--gray-200);
            display: flex;
            flex-direction: column;
            padding: var(--space-8) 0;
        }

        .sidebar-header {
            padding: 0 var(--space-8) var(--space-8);
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: var(--space-8);
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--primary);
            text-decoration: none;
            letter-spacing: 0.1em;
        }

        .nav-menu {
            flex: 1;
            padding: 0 var(--space-6);
        }

        .nav-section {
            margin-bottom: var(--space-8);
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 700;
            color: var(--gray-500);
            text-transform: uppercase;
            margin-bottom: var(--space-4);
            padding: 0 var(--space-4);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--gray-500);
            text-decoration: none;
            border-radius: var(--radius-md);
            margin-bottom: var(--space-2);
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-item:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }

        .nav-item.active {
            background: var(--primary-bg);
            color: var(--gray-900);
            font-weight: 600;
        }

        .nav-item.active::before {
            content: '';
            width: 8px;
            height: 8px;
            background: var(--green);
            border-radius: 50%;
            margin-right: var(--space-2);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--gray-50);
        }

        /* Header */
        .header {
            background: var(--white);
            padding: var(--space-6) var(--space-8);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .header-title {
            font-size: 46px;
            font-weight: 600;
            color: var(--gray-900);
            text-transform: capitalize;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .search-container {
            position: relative;
            width: 417px;
        }

        .search-input {
            width: 100%;
            padding: var(--space-3) var(--space-4) var(--space-3) 3rem;
            background: var(--gray-50);
            border: none;
            border-radius: var(--radius-md);
            font-size: 14px;
            color: var(--gray-500);
        }

        .search-icon {
            position: absolute;
            left: var(--space-4);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
        }

        .action-btn {
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--gray-500);
            border-radius: var(--radius-md);
            background: var(--white);
            color: var(--gray-500);
            font-weight: 500;
            text-transform: capitalize;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .action-btn:hover {
            background: var(--gray-100);
        }

        .action-btn.primary {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
        }

        .action-btn.primary:hover {
            background: var(--primary-dark);
        }

        /* User Profile */
        .user-profile {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .user-avatar {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            background: var(--gray-300);
            border: 1px solid var(--white);
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-size: 16px;
            font-weight: 400;
            color: var(--gray-900);
        }

        .user-location {
            font-size: 14px;
            color: var(--gray-500);
        }

        /* Content Area */
        .content-area {
            flex: 1;
            padding: var(--space-8);
            overflow-y: auto;
        }

        /* Stats Cards */
        .stats-section {
            margin-bottom: var(--space-8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-6);
        }

        .stat-card {
            background: var(--white);
            padding: var(--space-6);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: var(--space-2);
        }

        .stat-label {
            font-size: 14px;
            color: var(--gray-500);
            font-weight: 500;
        }

        /* Kanban Columns */
        .kanban-container {
            display: flex;
            gap: var(--space-6);
            margin-top: var(--space-8);
        }

        .kanban-column {
            flex: 1;
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            min-height: 625px;
        }

        .column-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
        }

        .column-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-900);
        }

        .column-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .column-indicator.todo { background: var(--primary); }
        .column-indicator.progress { background: var(--orange); }
        .column-indicator.done { background: var(--blue); }

        .column-count {
            background: var(--gray-200);
            color: var(--gray-600);
            font-size: 12px;
            font-weight: 500;
            padding: 2px 8px;
            border-radius: 10px;
            margin-left: auto;
        }

        /* Product Cards */
        .product-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-4);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            transition: all 0.2s ease;
        }

        .product-card:hover {
            box-shadow: var(--shadow-card);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: var(--space-4);
        }

        .card-status {
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
        }

        .card-status.low {
            background: var(--warning-bg);
            color: var(--warning);
        }

        .card-status.high {
            background: var(--danger-bg);
            color: var(--danger);
        }

        .card-status.completed {
            background: var(--success-bg);
            color: var(--success);
        }

        .card-menu {
            margin-left: auto;
            color: var(--gray-900);
            font-weight: 800;
            cursor: pointer;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--space-2);
        }

        .card-id {
            font-size: 16px;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: var(--space-3);
        }

        .card-description {
            font-size: 12px;
            color: var(--gray-500);
            line-height: 1.5;
            margin-bottom: var(--space-4);
        }

        .card-image {
            width: 100%;
            height: 110px;
            background: var(--gray-300);
            border-radius: 8px;
            margin-bottom: var(--space-4);
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-stats {
            display: flex;
            gap: var(--space-4);
            font-size: 12px;
            color: var(--gray-500);
        }

        .card-avatars {
            display: flex;
            gap: -8px;
        }

        .card-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--gray-300);
            border: 1px solid var(--white);
            margin-left: -8px;
        }

        .card-avatar:first-child {
            margin-left: 0;
        }

        /* Controls Section */
        .controls-section {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--space-4);
        }

        .controls-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .bulk-actions {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .selected-count {
            font-size: 14px;
            color: var(--gray-500);
        }

        .select-all-btn, .bulk-add-btn {
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--primary);
            border-radius: var(--radius-md);
            background: var(--white);
            color: var(--primary);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .bulk-add-btn {
            background: var(--primary);
            color: var(--white);
        }

        .select-all-btn:hover {
            background: var(--primary-bg);
        }

        .bulk-add-btn:hover {
            background: var(--primary-dark);
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-top: var(--space-6);
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: var(--space-12);
            color: var(--gray-500);
            font-size: 16px;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--space-2);
            margin-top: var(--space-8);
        }

        .pagination button {
            padding: var(--space-2) var(--space-3);
            border: 1px solid var(--gray-300);
            background: var(--white);
            color: var(--gray-700);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination button:hover {
            background: var(--gray-100);
        }

        .pagination button.active {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .app-container {
                margin: 10px;
                border-radius: var(--radius-lg);
            }

            .sidebar {
                width: 240px;
            }

            .header-title {
                font-size: 32px;
            }

            .search-container {
                width: 300px;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                margin: 0;
                border-radius: 0;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid var(--gray-200);
            }

            .header {
                flex-direction: column;
                gap: var(--space-4);
                align-items: stretch;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: space-between;
            }

            .search-container {
                width: 100%;
                order: -1;
            }

            .kanban-container {
                flex-direction: column;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="#" class="logo">R.A.V.E</a>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-item active">
                        <div class="nav-icon">🏠</div>
                        <span>Dashboard</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">📋</div>
                        <span>Upload Data</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">💬</div>
                        <span>Shopify Workspace</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">👥</div>
                        <span>Members</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">⚙️</div>
                        <span>Settings</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">My Projects</div>
                    <div class="nav-item active">
                        <span>Mobile App</span>
                    </div>
                    <div class="nav-item">
                        <span>Website Redesign</span>
                    </div>
                    <div class="nav-item">
                        <span>Design System</span>
                    </div>
                    <div class="nav-item">
                        <span>Wireframes</span>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Mobile App</h1>
                    <button class="action-btn primary">
                        <span>📊</span>
                    </button>
                    <button class="action-btn">
                        <span>⋯</span>
                    </button>
                </div>
                
                <div class="header-actions">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search for anything..." id="search-input">
                        <div class="search-icon">🔍</div>
                    </div>
                    
                    <button class="action-btn">
                        <span>📅</span>
                        Filter
                    </button>
                    
                    <button class="action-btn">
                        <span>📅</span>
                        Today
                    </button>
                    
                    <div class="user-profile">
                        <div class="user-info">
                            <div class="user-name">Anima Agrawal</div>
                            <div class="user-location">U.P, India</div>
                        </div>
                        <div class="user-avatar"></div>
                        <button class="action-btn">
                            <span>🔔</span>
                        </button>
                        <button class="action-btn">
                            <span>💬</span>
                        </button>
                        <button class="action-btn">
                            <span>⋯</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Stats Section -->
                <section class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="total-orders">0</div>
                            <div class="stat-label">Total Orders</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="selected-products">0</div>
                            <div class="stat-label">Selected</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="ready-products">0</div>
                            <div class="stat-label">Shopify Ready</div>
                        </div>
                    </div>
                </section>

                <!-- Controls Section -->
                <section class="controls-section">
                    <div class="controls-header">
                        <h2 class="controls-title">Product Selection</h2>
                        <div class="bulk-actions">
                            <span class="selected-count" id="selected-count">0 selected</span>
                            <button class="select-all-btn" id="select-all-btn">Select All</button>
                            <button class="bulk-add-btn" id="bulk-add-btn">Extract Images & Prepare for Shopify</button>
                        </div>
                    </div>
                </section>

                <!-- Kanban Layout for Products -->
                <div class="kanban-container">
                    <!-- To Do Column -->
                    <div class="kanban-column">
                        <div class="column-header">
                            <div class="column-indicator todo"></div>
                            <span class="column-title">To Do</span>
                            <span class="column-count" id="todo-count">0</span>
                        </div>
                        <div id="todo-products">
                            <!-- Sample Product Card -->
                            <div class="product-card">
                                <div class="card-header">
                                    <span class="card-status low">Low</span>
                                    <span class="card-menu">⋯</span>
                                </div>
                                <div class="card-title">Brainstorming</div>
                                <div class="card-id">01</div>
                                <div class="card-description">
                                    Brainstorming brings team members' diverse experience into play.
                                </div>
                                <div class="card-footer">
                                    <div class="card-stats">
                                        <span>💬 12 comments</span>
                                        <span>📁 0 files</span>
                                    </div>
                                    <div class="card-avatars">
                                        <div class="card-avatar"></div>
                                        <div class="card-avatar"></div>
                                        <div class="card-avatar"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- On Progress Column -->
                    <div class="kanban-column">
                        <div class="column-header">
                            <div class="column-indicator progress"></div>
                            <span class="column-title">On Progress</span>
                            <span class="column-count" id="progress-count">0</span>
                        </div>
                        <div id="progress-products">
                            <!-- Sample Product Card -->
                            <div class="product-card">
                                <div class="card-header">
                                    <span class="card-status completed">Completed</span>
                                    <span class="card-menu">⋯</span>
                                </div>
                                <div class="card-title">Design System</div>
                                <div class="card-id">02</div>
                                <div class="card-description">
                                    It just needs to adapt the UI from what you did before
                                </div>
                                <div class="card-footer">
                                    <div class="card-stats">
                                        <span>💬 12 comments</span>
                                        <span>📁 15 files</span>
                                    </div>
                                    <div class="card-avatars">
                                        <div class="card-avatar"></div>
                                        <div class="card-avatar"></div>
                                        <div class="card-avatar"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sample Product Card with Image -->
                            <div class="product-card">
                                <div class="card-header">
                                    <span class="card-status low">Low</span>
                                    <span class="card-menu">⋯</span>
                                </div>
                                <div class="card-title">Moodboard</div>
                                <div class="card-id">03</div>
                                <div class="card-image"></div>
                                <div class="card-footer">
                                    <div class="card-stats">
                                        <span>💬 9 comments</span>
                                        <span>📁 10 files</span>
                                    </div>
                                    <div class="card-avatars">
                                        <div class="card-avatar"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Done Column -->
                    <div class="kanban-column">
                        <div class="column-header">
                            <div class="column-indicator done"></div>
                            <span class="column-title">Done</span>
                            <span class="column-count" id="done-count">0</span>
                        </div>
                        <div id="done-products">
                            <!-- Sample Product Card -->
                            <div class="product-card">
                                <div class="card-header">
                                    <span class="card-status completed">Completed</span>
                                    <span class="card-menu">⋯</span>
                                </div>
                                <div class="card-title">Mobile App Design</div>
                                <div class="card-id">05</div>
                                <div class="card-image"></div>
                                <div class="card-footer">
                                    <div class="card-stats">
                                        <span>💬 12 comments</span>
                                        <span>📁 15 files</span>
                                    </div>
                                    <div class="card-avatars">
                                        <div class="card-avatar"></div>
                                        <div class="card-avatar"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Original Products Grid (Hidden by default, can be toggled) -->
                <div class="loading" id="loading" style="display: none;">Loading products...</div>
                <div class="products-grid" id="products-grid" style="display: none;"></div>
                <div class="pagination" id="pagination" style="display: none;"></div>
            </div>
        </main>
    </div>

    <script src="assets/js/app.js"></script>
    <script src="assets/js/dashboard-main.js"></script>

    <script>
        // Enhanced Dashboard Functionality
        class EnhancedDashboard {
            constructor() {
                this.selectedProducts = new Set();
                this.currentView = 'kanban'; // 'kanban' or 'grid'
                this.products = [];
                this.init();
            }

            async init() {
                await this.loadStats();
                await this.loadProducts();
                this.setupEventListeners();
                this.updateCounts();
            }

            async loadStats() {
                try {
                    const response = await fetch(`${window.app?.API_URL || '/api'}/dashboard/stats`);
                    const result = await response.json();

                    if (result.success) {
                        const stats = result.data;
                        document.getElementById('total-orders').textContent = stats.total_orders || 0;
                        document.getElementById('selected-products').textContent = this.selectedProducts.size;
                        document.getElementById('ready-products').textContent = stats.ready_products || 0;
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                    // Set demo data
                    document.getElementById('total-orders').textContent = '24';
                    document.getElementById('selected-products').textContent = '0';
                    document.getElementById('ready-products').textContent = '12';
                }
            }

            async loadProducts() {
                try {
                    const response = await fetch(`${window.app?.API_URL || '/api'}/products`);
                    const result = await response.json();

                    if (result.success) {
                        this.products = result.data;
                        this.renderProducts();
                    }
                } catch (error) {
                    console.error('Error loading products:', error);
                    // Use demo data
                    this.products = this.getDemoProducts();
                    this.renderProducts();
                }
            }

            getDemoProducts() {
                return [
                    {
                        id: 1,
                        title: 'Wireless Bluetooth Headphones',
                        status: 'todo',
                        priority: 'low',
                        description: 'High-quality wireless headphones with noise cancellation',
                        comments: 5,
                        files: 3,
                        image: null
                    },
                    {
                        id: 2,
                        title: 'Smart Watch Series 5',
                        status: 'progress',
                        priority: 'high',
                        description: 'Latest smartwatch with health monitoring features',
                        comments: 8,
                        files: 12,
                        image: 'placeholder'
                    },
                    {
                        id: 3,
                        title: 'USB-C Fast Charger',
                        status: 'done',
                        priority: 'completed',
                        description: 'Fast charging cable compatible with all devices',
                        comments: 3,
                        files: 5,
                        image: null
                    }
                ];
            }

            renderProducts() {
                if (this.currentView === 'kanban') {
                    this.renderKanbanView();
                } else {
                    this.renderGridView();
                }
                this.updateCounts();
            }

            renderKanbanView() {
                const todoContainer = document.getElementById('todo-products');
                const progressContainer = document.getElementById('progress-products');
                const doneContainer = document.getElementById('done-products');

                // Clear existing content except samples
                this.clearProductContainers();

                this.products.forEach(product => {
                    const card = this.createProductCard(product);

                    switch(product.status) {
                        case 'todo':
                            todoContainer.appendChild(card);
                            break;
                        case 'progress':
                            progressContainer.appendChild(card);
                            break;
                        case 'done':
                            doneContainer.appendChild(card);
                            break;
                    }
                });
            }

            renderGridView() {
                const gridContainer = document.getElementById('products-grid');
                gridContainer.innerHTML = '';

                this.products.forEach(product => {
                    const card = this.createProductCard(product);
                    gridContainer.appendChild(card);
                });

                document.getElementById('products-grid').style.display = 'grid';
                document.querySelector('.kanban-container').style.display = 'none';
            }

            createProductCard(product) {
                const card = document.createElement('div');
                card.className = 'product-card';
                card.dataset.productId = product.id;

                const statusClass = product.priority === 'completed' ? 'completed' :
                                  product.priority === 'high' ? 'high' : 'low';

                card.innerHTML = `
                    <div class="card-header">
                        <span class="card-status ${statusClass}">${product.priority}</span>
                        <span class="card-menu">⋯</span>
                    </div>
                    <div class="card-title">${product.title}</div>
                    <div class="card-id">${String(product.id).padStart(2, '0')}</div>
                    ${product.description ? `<div class="card-description">${product.description}</div>` : ''}
                    ${product.image ? '<div class="card-image"></div>' : ''}
                    <div class="card-footer">
                        <div class="card-stats">
                            <span>💬 ${product.comments} comments</span>
                            <span>📁 ${product.files} files</span>
                        </div>
                        <div class="card-avatars">
                            <div class="card-avatar"></div>
                            <div class="card-avatar"></div>
                            ${Math.random() > 0.5 ? '<div class="card-avatar"></div>' : ''}
                        </div>
                    </div>
                `;

                // Add click handler for selection
                card.addEventListener('click', (e) => {
                    if (e.target.classList.contains('card-menu')) return;
                    this.toggleProductSelection(product.id, card);
                });

                return card;
            }

            clearProductContainers() {
                // Keep sample cards, remove dynamically added ones
                const containers = ['todo-products', 'progress-products', 'done-products'];
                containers.forEach(containerId => {
                    const container = document.getElementById(containerId);
                    const dynamicCards = container.querySelectorAll('[data-product-id]');
                    dynamicCards.forEach(card => card.remove());
                });
            }

            toggleProductSelection(productId, cardElement) {
                if (this.selectedProducts.has(productId)) {
                    this.selectedProducts.delete(productId);
                    cardElement.classList.remove('selected');
                } else {
                    this.selectedProducts.add(productId);
                    cardElement.classList.add('selected');
                }

                this.updateSelectedCount();
            }

            updateSelectedCount() {
                document.getElementById('selected-count').textContent = `${this.selectedProducts.size} selected`;
                document.getElementById('selected-products').textContent = this.selectedProducts.size;
            }

            updateCounts() {
                const todoCount = this.products.filter(p => p.status === 'todo').length;
                const progressCount = this.products.filter(p => p.status === 'progress').length;
                const doneCount = this.products.filter(p => p.status === 'done').length;

                document.getElementById('todo-count').textContent = todoCount;
                document.getElementById('progress-count').textContent = progressCount;
                document.getElementById('done-count').textContent = doneCount;
            }

            setupEventListeners() {
                // Select All button
                document.getElementById('select-all-btn')?.addEventListener('click', () => {
                    if (this.selectedProducts.size === this.products.length) {
                        this.selectedProducts.clear();
                        document.querySelectorAll('.product-card').forEach(card => {
                            card.classList.remove('selected');
                        });
                    } else {
                        this.products.forEach(product => {
                            this.selectedProducts.add(product.id);
                        });
                        document.querySelectorAll('.product-card').forEach(card => {
                            card.classList.add('selected');
                        });
                    }
                    this.updateSelectedCount();
                });

                // Bulk Add button
                document.getElementById('bulk-add-btn')?.addEventListener('click', () => {
                    if (this.selectedProducts.size === 0) {
                        alert('Please select products first');
                        return;
                    }

                    // Here you would integrate with the existing bulk processing functionality
                    console.log('Processing products:', Array.from(this.selectedProducts));
                    alert(`Processing ${this.selectedProducts.size} products...`);
                });

                // Search functionality
                document.getElementById('search-input')?.addEventListener('input', (e) => {
                    this.filterProducts(e.target.value);
                });
            }

            filterProducts(searchTerm) {
                const filteredProducts = this.products.filter(product =>
                    product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    product.description.toLowerCase().includes(searchTerm.toLowerCase())
                );

                // Temporarily replace products for rendering
                const originalProducts = this.products;
                this.products = filteredProducts;
                this.renderProducts();
                this.products = originalProducts;
            }
        }

        // Initialize enhanced dashboard when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new EnhancedDashboard();
        });

        // Add selected state styles
        const style = document.createElement('style');
        style.textContent = `
            .product-card.selected {
                border-color: var(--primary);
                box-shadow: 0 0 0 2px rgba(80, 48, 229, 0.2);
                transform: translateY(-2px);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
