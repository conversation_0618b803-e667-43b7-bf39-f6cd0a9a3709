# PowerShell script to test file upload
$uri = "http://localhost:5000/api/upload/json"
$filePath = "test-data.json"

# Read file content
$fileContent = Get-Content -Path $filePath -Raw
$boundary = [System.Guid]::NewGuid().ToString()

# Create multipart form data
$LF = "`r`n"
$bodyLines = (
    "--$boundary",
    "Content-Disposition: form-data; name=`"file`"; filename=`"test-data.json`"",
    "Content-Type: application/json$LF",
    $fileContent,
    "--$boundary--$LF"
) -join $LF

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary"
    Write-Host "Upload successful:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Upload failed:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody"
    }
}
