/* R.A.V.E - Clean Minimalist Dashboard */

/* MatterSq Font Import */
@font-face {
    font-family: "Matter SQ";
    src: url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.eot");
    src: url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.eot?#iefix") format("embedded-opentype"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.woff") format("woff"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.woff2") format("woff2"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.ttf") format("truetype"),
         url("../../../../Web Fonts/8611cf274fd66935b8f771ac84029521.svg#Matter SQ Regular") format("svg");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Design System */
:root {
    /* Colors */
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-primary: "Matter SQ", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Layout */
    --sidebar-width: 280px;
    --header-height: 64px;
}

/* Reset */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: var(--font-primary);
    font-size: 14px;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100%;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.25;
    color: var(--gray-900);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Layout */
.app-container {
    display: flex;
    min-height: 100vh;
    height: 100vh;
    width: 100vw;
    position: relative;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    height: var(--header-height);
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-6);
    z-index: 40;
    transition: left var(--transition);
}

.header.expanded {
    left: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.header-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.header-title i {
    color: var(--primary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    z-index: 50;
    transition: transform var(--transition);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--gray-200);
    padding: 0 var(--space-6);
}

.logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--primary);
    text-decoration: none;
}

.nav-menu {
    padding: var(--space-6) 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--gray-600);
    text-decoration: none;
    transition: all var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.nav-item.active {
    background: var(--primary);
    color: var(--white);
    border-left-color: var(--primary-dark);
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: var(--space-8);
    min-height: calc(100vh - var(--header-height));
    height: calc(100vh - var(--header-height));
    width: calc(100vw - var(--sidebar-width));
    overflow-y: auto;
    transition: margin-left var(--transition);
}

.main-content.expanded {
    margin-left: 0;
}

/* Content Sections */
.content-section {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.section-title i {
    color: var(--primary);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all var(--transition);
    text-decoration: none;
    white-space: nowrap;
    font-family: var(--font-primary);
}

.btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-800);
}

.btn-success {
    background: var(--success);
    color: var(--white);
    border-color: var(--success);
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
    border-color: var(--warning);
}

.btn-error {
    background: var(--error);
    color: var(--white);
    border-color: var(--error);
}

.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
}

/* Form Elements */
.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.form-input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-family: var(--font-primary);
    transition: all var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-family: var(--font-primary);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition);
}

.form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-body {
    color: var(--gray-600);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.primary {
    background: var(--primary);
}

.stat-icon.success {
    background: var(--success);
}

.stat-icon.warning {
    background: var(--warning);
}

.stat-icon.accent {
    background: var(--accent);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-12);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition);
    background: var(--gray-50);
}

.upload-area:hover {
    border-color: var(--primary);
    background: var(--white);
}

.upload-area.active {
    border-color: var(--primary);
    background: rgba(37, 99, 235, 0.05);
}

.upload-content i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: var(--space-4);
}

.upload-content h3 {
    margin-bottom: var(--space-2);
    color: var(--gray-700);
}

.upload-content p {
    color: var(--gray-500);
    margin: 0;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-6);
}

.product-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition);
}

.product-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--gray-100);
}

.product-content {
    padding: var(--space-4);
}

.product-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: var(--space-3);
}

.product-actions {
    display: flex;
    gap: var(--space-2);
}

/* Orders Table */
.orders-table {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.table td {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-content {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-content i {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: var(--space-4);
}

.loading-content p {
    margin: 0;
    color: var(--gray-700);
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 200;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.toast {
    background: var(--white);
    border-radius: var(--radius);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary);
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left-color: var(--success);
}

.toast.warning {
    border-left-color: var(--warning);
}

.toast.error {
    border-left-color: var(--error);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.toast-icon {
    font-size: 1.25rem;
}

.toast-message {
    flex: 1;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-1);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.text-primary {
    color: var(--primary);
}

.text-success {
    color: var(--success);
}

.text-warning {
    color: var(--warning);
}

.text-error {
    color: var(--error);
}

/* Upload Progress */
.upload-progress {
    margin-top: var(--space-6);
    padding: var(--space-4);
    background: var(--white);
    border-radius: var(--radius);
    border: 1px solid var(--gray-200);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--space-3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-sm);
    transition: width var(--transition);
    width: 0%;
}

.progress-details {
    font-size: 14px;
    color: var(--gray-600);
    text-align: center;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.toast {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary);
    min-width: 300px;
    animation: slideIn var(--transition);
}

.toast-success {
    border-left-color: var(--success);
}

.toast-error {
    border-left-color: var(--error);
}

.toast-warning {
    border-left-color: var(--warning);
}

.toast-info {
    border-left-color: var(--primary);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex: 1;
}

.toast-content i {
    font-size: 16px;
}

.toast-success .toast-content i {
    color: var(--success);
}

.toast-error .toast-content i {
    color: var(--error);
}

.toast-warning .toast-content i {
    color: var(--warning);
}

.toast-info .toast-content i {
    color: var(--primary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.toast-close:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .header {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block !important;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }

    .section-actions {
        width: 100%;
        flex-direction: column;
    }

    .toast-container {
        left: var(--space-4);
        right: var(--space-4);
    }

    .toast {
        min-width: auto;
    }
}
