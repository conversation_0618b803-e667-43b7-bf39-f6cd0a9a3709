"""
Product management routes
"""
from flask import Blueprint, request, jsonify
from services.mongodb_service import mongodb_service
from services.pricing_service import PricingService
import logging

logger = logging.getLogger(__name__)

products_bp = Blueprint('products', __name__)
pricing_service = PricingService()

@products_bp.route('/', methods=['GET'])
def get_products():
    """Get products with pagination and filtering"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 50)), 100)
        skip = (page - 1) * limit
        
        # Build filters
        filters = {}
        
        # Status filter
        status = request.args.get('status')
        if status:
            filters['status'] = status
        
        # Search filter
        search = request.args.get('search')
        if search:
            filters['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'original_content.title': {'$regex': search, '$options': 'i'}},
                {'product_id': {'$regex': search, '$options': 'i'}}
            ]
        
        # Price range filter
        min_price = request.args.get('min_price')
        max_price = request.args.get('max_price')
        if min_price or max_price:
            price_filter = {}
            if min_price:
                price_filter['$gte'] = float(min_price)
            if max_price:
                price_filter['$lte'] = float(max_price)
            filters['final_price'] = price_filter
        
        # Get products
        products = mongodb_service.get_products(skip=skip, limit=limit, filters=filters)
        
        # Get total count for pagination
        total = mongodb_service.db.products.count_documents(filters)
        
        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'total_pages': (total + limit - 1) // limit
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting products: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/<product_id>', methods=['GET'])
def get_product(product_id):
    """Get single product by ID"""
    try:
        product = mongodb_service.get_product(product_id)
        
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': product
        })
        
    except Exception as e:
        logger.error(f"Error getting product: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/<product_id>', methods=['PUT'])
def update_product(product_id):
    """Update product"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Remove fields that shouldn't be updated directly
        protected_fields = ['_id', 'product_id', 'created_at', 'import_batch_id']
        for field in protected_fields:
            data.pop(field, None)
        
        success = mongodb_service.update_product(product_id, data)
        
        if not success:
            return jsonify({
                'success': False,
                'error': 'Failed to update product'
            }), 500
        
        # Get updated product
        updated_product = mongodb_service.get_product(product_id)
        
        return jsonify({
            'success': True,
            'data': updated_product
        })
        
    except Exception as e:
        logger.error(f"Error updating product: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/<product_id>', methods=['DELETE'])
def delete_product(product_id):
    """Delete product"""
    try:
        success = mongodb_service.delete_product(product_id)
        
        if not success:
            return jsonify({
                'success': False,
                'error': 'Product not found or failed to delete'
            }), 404
        
        return jsonify({
            'success': True,
            'message': 'Product deleted successfully'
        })
        
    except Exception as e:
        logger.error(f"Error deleting product: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/bulk-update', methods=['POST'])
def bulk_update_products():
    """Bulk update products"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data or 'updates' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids and updates are required'
            }), 400
        
        product_ids = data['product_ids']
        updates = data['updates']
        
        # Remove protected fields
        protected_fields = ['_id', 'product_id', 'created_at', 'import_batch_id']
        for field in protected_fields:
            updates.pop(field, None)
        
        updated_count = 0
        errors = []
        
        for product_id in product_ids:
            try:
                success = mongodb_service.update_product(product_id, updates)
                if success:
                    updated_count += 1
                else:
                    errors.append(f"Failed to update product {product_id}")
            except Exception as e:
                errors.append(f"Error updating product {product_id}: {str(e)}")
        
        return jsonify({
            'success': True,
            'data': {
                'updated_count': updated_count,
                'total_requested': len(product_ids),
                'errors': errors
            }
        })
        
    except Exception as e:
        logger.error(f"Error in bulk update: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/bulk-delete', methods=['POST'])
def bulk_delete_products():
    """Bulk delete products"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        
        deleted_count = 0
        errors = []
        
        for product_id in product_ids:
            try:
                success = mongodb_service.delete_product(product_id)
                if success:
                    deleted_count += 1
                else:
                    errors.append(f"Failed to delete product {product_id}")
            except Exception as e:
                errors.append(f"Error deleting product {product_id}: {str(e)}")
        
        return jsonify({
            'success': True,
            'data': {
                'deleted_count': deleted_count,
                'total_requested': len(product_ids),
                'errors': errors
            }
        })
        
    except Exception as e:
        logger.error(f"Error in bulk delete: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/apply-pricing', methods=['POST'])
def apply_pricing():
    """Apply pricing rules to products"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Get pricing parameters
        product_ids = data.get('product_ids', [])
        rule_type = data.get('rule_type', 'percentage')
        markup_value = data.get('markup_value', 0)
        
        if not product_ids:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        # Apply pricing
        results = pricing_service.apply_pricing_to_products(
            product_ids, rule_type, markup_value
        )
        
        return jsonify({
            'success': True,
            'data': results
        })
        
    except Exception as e:
        logger.error(f"Error applying pricing: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/calculate-pricing', methods=['POST'])
def calculate_pricing():
    """Calculate pricing preview without applying"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        product_ids = data.get('product_ids', [])
        rule_type = data.get('rule_type', 'percentage')
        markup_value = data.get('markup_value', 0)
        
        if not product_ids:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        # Calculate pricing preview
        preview = pricing_service.calculate_pricing_preview(
            product_ids, rule_type, markup_value
        )
        
        return jsonify({
            'success': True,
            'data': preview
        })
        
    except Exception as e:
        logger.error(f"Error calculating pricing: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/stats')
def get_product_stats():
    """Get product statistics"""
    try:
        stats = {
            'total_products': mongodb_service.db.products.count_documents({}),
            'by_status': {},
            'price_ranges': {},
            'recent_products': mongodb_service.db.products.count_documents({
                'created_at': {'$gte': datetime.now() - timedelta(days=7)}
            })
        }
        
        # Get status distribution
        status_pipeline = [
            {'$group': {'_id': '$status', 'count': {'$sum': 1}}}
        ]
        status_results = list(mongodb_service.db.products.aggregate(status_pipeline))
        for result in status_results:
            stats['by_status'][result['_id']] = result['count']
        
        # Get price ranges
        price_pipeline = [
            {
                '$bucket': {
                    'groupBy': '$final_price',
                    'boundaries': [0, 10, 25, 50, 100, 1000],
                    'default': '1000+',
                    'output': {'count': {'$sum': 1}}
                }
            }
        ]
        price_results = list(mongodb_service.db.products.aggregate(price_pipeline))
        for result in price_results:
            range_key = f"${result['_id']}-{result['_id'] + 10 if result['_id'] < 1000 else '+'}"
            stats['price_ranges'][range_key] = result['count']
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting product stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@products_bp.route('/export', methods=['POST'])
def export_products():
    """Export products to CSV/JSON"""
    try:
        data = request.get_json()
        export_format = data.get('format', 'csv')
        product_ids = data.get('product_ids', [])
        
        if not product_ids:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        # Get products
        products = []
        for product_id in product_ids:
            product = mongodb_service.get_product(product_id)
            if product:
                products.append(product)
        
        if export_format == 'csv':
            # Generate CSV
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                'Product ID', 'Title', 'Original Price', 'Final Price',
                'Status', 'Currency', 'Created At'
            ])
            
            # Write data
            for product in products:
                writer.writerow([
                    product.get('product_id', ''),
                    product.get('title', ''),
                    product.get('original_price', 0),
                    product.get('final_price', 0),
                    product.get('status', ''),
                    product.get('currency', ''),
                    product.get('created_at', '')
                ])
            
            csv_data = output.getvalue()
            output.close()
            
            return jsonify({
                'success': True,
                'data': {
                    'format': 'csv',
                    'content': csv_data,
                    'filename': f'products_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
                }
            })
        
        else:  # JSON format
            return jsonify({
                'success': True,
                'data': {
                    'format': 'json',
                    'content': products,
                    'filename': f'products_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
                }
            })
        
    except Exception as e:
        logger.error(f"Error exporting products: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500