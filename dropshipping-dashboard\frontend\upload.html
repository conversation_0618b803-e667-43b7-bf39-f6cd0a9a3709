<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Data - R.A.V.E</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            background: #ffffff;
            margin: 0;
            padding: 0;
            color: #1f2937;
            line-height: 1.6;
        }

        /* Navigation Header */
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2563eb;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .nav-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .nav-btn.secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .nav-btn:hover {
            background: #2563eb;
            color: white;
        }

        .nav-btn.secondary:hover {
            background: #e2e8f0;
            color: #374151;
        }

        .main-content {
            padding: 40px 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1e293b;
            margin-bottom: 30px;
            text-align: center;
        }

        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .upload-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }

        .upload-btn:hover {
            background: #2563eb;
        }

        .progress-container {
            display: none;
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: #10b981;
        }

        .toast.error {
            background: #ef4444;
        }

        /* Database Management */
        .database-management {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #e2e8f0;
        }

        .database-management h2 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .danger-zone {
            background: #fef2f2;
            border: 2px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .danger-zone h3 {
            color: #dc2626;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .danger-zone p {
            color: #7f1d1d;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .nuke-btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nuke-btn:hover {
            background: #b91c1c;
            transform: translateY(-1px);
        }

        .nuke-btn:active {
            transform: translateY(0);
        }

        .stats {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .stats h3 {
            margin-bottom: 15px;
            color: #1e293b;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .hidden {
            display: none !important;
        }

        #file-input {
            display: none;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                R.A.V.E
            </div>
            <nav class="nav-buttons">
                <a href="index.html" class="nav-btn secondary">Dashboard</a>
                <a href="upload.html" class="nav-btn primary">Upload Data</a>
                <a href="workspace.html" class="nav-btn secondary">Shopify Workspace</a>
            </nav>
        </div>
    </header>

    <div class="main-content">
        <div class="container">
        <h1>📤 Data Management</h1>
        
        <div class="upload-area" id="upload-area">
            <p style="margin-bottom: 15px; color: #64748b;">
                Drop your download.json file here or click to browse
            </p>
            <button class="upload-btn" id="upload-btn">Choose File</button>
            <input type="file" id="file-input" accept=".json">
        </div>

        <div class="progress-container" id="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text" id="progress-text">Uploading...</div>
        </div>

        <div class="stats" id="stats">
            <h3>Dashboard Stats</h3>
            <div class="stats-grid" id="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="total-products">0</div>
                    <div class="stat-label">Products</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-orders">0</div>
                    <div class="stat-label">Orders</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-batches">0</div>
                    <div class="stat-label">Batches</div>
                </div>
            </div>

            <!-- Database Management Section -->
            <div class="database-management">
                <h2>Database Management</h2>
                <div class="danger-zone">
                    <h3>⚠️ Danger Zone</h3>
                    <p>This action will permanently delete ALL data from the database including products, orders, and import batches.</p>
                    <button class="nuke-btn" id="nuke-btn">Nuke Database</button>

                    <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #fecaca;">
                        <h4>Reset Product Statuses</h4>
                        <p style="font-size: 0.9rem; margin-bottom: 1rem;">Reset all products to 'imported' status and clear workspace assignments.</p>
                        <button class="reset-btn" id="reset-statuses-btn" style="background: #f59e0b; color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; font-weight: 600; cursor: pointer;">Reset Statuses</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        // Configuration
        const API_URL = 'http://localhost:5000/api';

        // DOM Elements
        const uploadArea = document.getElementById('upload-area');
        const uploadBtn = document.getElementById('upload-btn');
        const fileInput = document.getElementById('file-input');
        const progressContainer = document.getElementById('progress-container');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        // Toast function
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // Load dashboard stats
        async function loadStats() {
            try {
                const response = await fetch(`${API_URL}/dashboard/stats`);
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('total-products').textContent = stats.total_products || 0;
                    document.getElementById('total-orders').textContent = stats.total_orders || 0;
                    document.getElementById('total-batches').textContent = stats.total_import_batches || 0;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Upload file function
        async function uploadFile(file) {
            if (!file || file.type !== 'application/json') {
                showToast('Please select a valid JSON file', 'error');
                return;
            }

            // Show progress
            progressContainer.style.display = 'block';
            progressFill.style.width = '10%';
            progressText.textContent = 'Uploading file...';

            const formData = new FormData();
            formData.append('file', file);

            try {
                // Upload file
                progressFill.style.width = '30%';
                progressText.textContent = 'Processing upload...';
                
                const response = await fetch(`${API_URL}/upload/json`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    progressFill.style.width = '60%';
                    progressText.textContent = 'Processing data...';
                    
                    // Start polling for status
                    const batchId = result.data.batch_id;
                    await pollStatus(batchId);
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                console.error('Upload error:', error);
                showToast('Upload failed: ' + error.message, 'error');
                progressContainer.style.display = 'none';
            }
        }

        // Poll upload status
        async function pollStatus(batchId) {
            const maxAttempts = 30;
            let attempts = 0;

            const poll = async () => {
                try {
                    attempts++;
                    const response = await fetch(`${API_URL}/upload/status/${batchId}`);
                    const result = await response.json();

                    if (result.success) {
                        const batch = result.data;
                        
                        if (batch.status === 'completed') {
                            progressFill.style.width = '100%';
                            progressText.textContent = 'Processing completed!';
                            
                            const ordersCount = batch.processed_orders || 0;
                            const productsCount = batch.processed_products || 0;
                            
                            showToast(`Upload successful! Processed ${ordersCount} orders and ${productsCount} products.`, 'success');
                            
                            // Refresh stats
                            setTimeout(() => {
                                loadStats();
                                progressContainer.style.display = 'none';
                                progressFill.style.width = '0%';
                            }, 2000);
                            
                            return;
                        } else if (batch.status === 'failed') {
                            throw new Error(batch.error_message || 'Processing failed');
                        } else {
                            // Still processing
                            const processed = (batch.processed_orders || 0) + (batch.processed_products || 0);
                            const total = (batch.total_orders || 0) + (batch.total_products || 0);
                            
                            if (total > 0) {
                                const percentage = Math.min(90, 60 + (processed / total) * 30);
                                progressFill.style.width = percentage + '%';
                                progressText.textContent = `Processing... ${processed}/${total} items`;
                            }
                        }
                    }

                    if (attempts < maxAttempts) {
                        setTimeout(poll, 2000);
                    } else {
                        throw new Error('Processing timeout');
                    }
                } catch (error) {
                    console.error('Polling error:', error);
                    showToast('Processing failed: ' + error.message, 'error');
                    progressContainer.style.display = 'none';
                }
            };

            poll();
        }

        // Event listeners
        uploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                uploadFile(e.target.files[0]);
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files[0]) {
                uploadFile(files[0]);
            }
        });

        // Nuke database functionality
        document.getElementById('nuke-btn').addEventListener('click', async () => {
            if (confirm('💥 NUCLEAR OPTION: Are you sure you want to NUKE the entire database? This action cannot be undone!')) {
                if (confirm('🚨 FINAL WARNING: This will permanently delete ALL products, orders, batches, and everything in the database. Type YES to confirm.')) {
                    try {
                        showToast('💥 Nuking database...', 'info');

                        const response = await fetch(`${API_URL}/database/clear`, {
                            method: 'POST'
                        });

                        const result = await response.json();

                        if (result.success) {
                            showToast('💥 Database nuked successfully! All data destroyed.', 'success');
                            loadStats(); // Refresh stats
                        } else {
                            throw new Error(result.error || 'Failed to clear database');
                        }
                    } catch (error) {
                        console.error('Clear database error:', error);
                        showToast('Failed to clear database: ' + error.message, 'error');
                    }
                }
            }
        });

        // Reset product statuses functionality
        document.getElementById('reset-statuses-btn').addEventListener('click', async () => {
            if (confirm('🔄 Are you sure you want to reset all product statuses? This will clear workspace assignments.')) {
                try {
                    showToast('🔄 Resetting product statuses...', 'info');

                    const response = await fetch(`${API_URL}/workspace/reset-statuses`, {
                        method: 'POST'
                    });

                    const result = await response.json();

                    if (result.success) {
                        showToast(`✅ Reset ${result.data.reset_count} products to imported status`, 'success');
                        loadStats(); // Refresh stats
                    } else {
                        throw new Error(result.error || 'Failed to reset statuses');
                    }
                } catch (error) {
                    console.error('Reset statuses error:', error);
                    showToast('Failed to reset statuses: ' + error.message, 'error');
                }
            }
        });

        // Load initial stats
        loadStats();
    </script>
</body>
</html>
