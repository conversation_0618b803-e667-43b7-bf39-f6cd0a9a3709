/* Enhanced Dashboard Styles */

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    animation: fadeInUp 0.6s ease-out;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    background: var(--bg-card);
    backdrop-filter: var(--glass-backdrop);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    border: 1px solid var(--glass-border);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-card-hover);
    background: var(--bg-card-hover);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-icon {
    font-size: 2.5rem;
    background: var(--primary-gradient);
    color: var(--text-white);
    width: 5rem;
    height: 5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-glow);
    position: relative;
}

.stat-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: var(--primary-gradient);
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-content p {
    margin: 0;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Enhanced Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-xl);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.quick-actions h3 {
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background: var(--bg-card);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-card);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-card-hover);
}

.action-btn i {
    font-size: 2.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all var(--transition-normal);
}

.action-btn:hover i {
    transform: scale(1.2) rotate(5deg);
}

.action-btn span {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

/* Recent Activity */
.recent-activity {
    margin-bottom: var(--spacing-xl);
}

.recent-activity h3 {
    margin-bottom: var(--spacing-md);
}

.activity-list {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

/* Import Section */
.upload-area {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-tertiary);
}

.upload-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.upload-info {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: var(--spacing-md);
}

.upload-progress {
    margin-top: var(--spacing-lg);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.progress-bar {
    width: 100%;
    height: 1rem;
    background-color: var(--bg-tertiary);
    border-radius: 9999px;
    overflow: hidden;
}

.progress-fill {
    width: 0;
    height: 100%;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.progress-details {
    margin-top: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Enhanced Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    animation: fadeInUp 1.4s ease-out 0.8s both;
}

.product-card {
    background: var(--bg-card);
    backdrop-filter: var(--glass-backdrop);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--glass-border);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-card-hover);
}

.product-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
    background: var(--bg-tertiary);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-card:hover .product-image-overlay {
    opacity: 1;
}

.product-actions {
    position: absolute;
    bottom: var(--spacing-md);
    left: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateY(0);
}

.product-action-btn {
    flex: 1;
    padding: var(--spacing-sm);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-white);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.product-action-btn:hover {
    background: var(--primary-color);
    transform: scale(1.05);
}

.product-content {
    padding: var(--spacing-lg);
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.price-original {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-decoration: line-through;
}

.price-final {
    font-size: 1.25rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.product-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: var(--warning-gradient);
    color: var(--text-white);
}

.status-processing {
    background: var(--info-gradient);
    color: var(--text-white);
}

.status-completed {
    background: var(--success-gradient);
    color: var(--text-white);
}

.status-failed {
    background: var(--error-gradient);
    color: var(--text-white);
}

/* Enhanced Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
    animation: fadeInUp 1.6s ease-out 1s both;
}

.pagination-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-card);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 40px;
    text-align: center;
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.pagination-btn.active {
    background: var(--primary-gradient);
    color: var(--text-white);
    box-shadow: var(--shadow-glow);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}