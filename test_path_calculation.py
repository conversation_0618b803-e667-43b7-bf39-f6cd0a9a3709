#!/usr/bin/env python3
import os

# Simulate the image service file location
image_service_path = os.path.abspath('dropshipping-dashboard/backend/services/image_service.py')
print(f"Image service path: {image_service_path}")

# Calculate each step
step1 = os.path.dirname(image_service_path)
print(f"Step 1 (services dir): {step1}")

step2 = os.path.dirname(step1)
print(f"Step 2 (backend dir): {step2}")

step3 = os.path.dirname(step2)
print(f"Step 3 (dropshipping-dashboard dir): {step3}")

step4 = os.path.dirname(step3)
print(f"Step 4 (root dir): {step4}")

# Final downloader path
downloader_path = os.path.join(step4, 'aliexpress_image_downloader.py')
print(f"Downloader path: {downloader_path}")
print(f"Exists: {os.path.exists(downloader_path)}")

# Check current directory
print(f"Current working directory: {os.getcwd()}")
print(f"Downloader in current dir: {os.path.exists('aliexpress_image_downloader.py')}")
