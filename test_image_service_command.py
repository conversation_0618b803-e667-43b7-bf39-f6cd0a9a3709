#!/usr/bin/env python3
"""
Test script to mimic exactly what the image service is doing
"""
import os
import sys
import subprocess
import tempfile

def test_image_service_command():
    print("🧪 Testing Image Service Command")
    print("=" * 40)
    
    # Mimic the exact command the image service would run
    url = "https://www.aliexpress.com/item/3256808519179538.html"
    download_dir = "test_service_download"
    max_workers = 5
    
    # Create download directory
    os.makedirs(download_dir, exist_ok=True)
    
    # Path to downloader (same calculation as image service)
    current_file = __file__
    downloader_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file)))),
        'aliexpress_image_downloader.py'
    )
    
    print(f"Current file: {current_file}")
    print(f"Calculated downloader path: {downloader_path}")
    print(f"Downloader exists: {os.path.exists(downloader_path)}")
    
    # Prepare command exactly like image service
    cmd = [
        sys.executable,
        downloader_path,
        '-u', url,
        '-d', download_dir,
        '-w', str(max_workers)
    ]
    
    print(f"\nCommand: {' '.join(cmd)}")
    
    try:
        # Run the command exactly like image service
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        print(f"\nReturn code: {result.returncode}")
        print(f"STDOUT:\n{result.stdout}")
        print(f"STDERR:\n{result.stderr}")
        
        if result.returncode == 0:
            print("✅ Command executed successfully")
            
            # Check what was downloaded
            print(f"\nChecking download directory: {download_dir}")
            if os.path.exists(download_dir):
                for root, dirs, files in os.walk(download_dir):
                    level = root.replace(download_dir, '').count(os.sep)
                    indent = ' ' * 2 * level
                    print(f"{indent}{os.path.basename(root)}/")
                    subindent = ' ' * 2 * (level + 1)
                    for file in files:
                        print(f"{subindent}{file}")
            else:
                print("❌ Download directory not created")
        else:
            print(f"❌ Command failed with return code: {result.returncode}")
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out")
    except Exception as e:
        print(f"❌ Error running command: {e}")

if __name__ == "__main__":
    test_image_service_command()
