#!/usr/bin/env python3

from services.mongodb_service import mongodb_service

# Get a product with images
product = mongodb_service.db.products.find_one({'images': {'$exists': True, '$ne': []}})
if product:
    print('Product ID:', str(product['_id']))
    print('Images:')
    for i, img in enumerate(product.get('images', [])):
        print(f'  Image {i+1}: {img.get("url", "No URL")}')
        print(f'    Local path: {img.get("local_path", "No path")}')
else:
    print('No products with images found')
