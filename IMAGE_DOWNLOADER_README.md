# AliExpress Image Downloader

A Python application for downloading product images from AliExpress URLs with support for batch processing, progress tracking, and organized file storage.

## Features

- ✅ Download all product images from AliExpress URLs
- ✅ Batch processing of multiple URLs
- ✅ Concurrent downloads for faster processing
- ✅ Progress tracking with visual progress bars
- ✅ Organized file storage with product folders
- ✅ Metadata saving (JSON format)
- ✅ Comprehensive error handling and logging
- ✅ Command-line interface
- ✅ Support for URL files
- ✅ High-quality image extraction

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify installation:**
   ```bash
   python aliexpress_image_downloader.py --version
   ```

## Usage

### Basic Usage

**Download from a single URL:**
```bash
python aliexpress_image_downloader.py -u "https://www.aliexpress.com/item/**********.html"
```

**Download from multiple URLs:**
```bash
python aliexpress_image_downloader.py -u "url1" "url2" "url3"
```

**Download from a file containing URLs:**
```bash
python aliexpress_image_downloader.py -f urls.txt
```

### Advanced Options

**Specify custom download directory:**
```bash
python aliexpress_image_downloader.py -u "url" -d "my_downloads"
```

**Use more concurrent workers for faster downloads:**
```bash
python aliexpress_image_downloader.py -u "url" -w 10
```

**Enable verbose logging:**
```bash
python aliexpress_image_downloader.py -u "url" -v
```

## Output Structure

The application creates an organized folder structure:

```
downloads/
├── **********_Product-Name/
│   ├── image_001.jpg
│   ├── image_002.jpg
│   ├── image_003.jpg
│   └── metadata.json
├── 0987654321_Another-Product/
│   ├── image_001.jpg
│   ├── image_002.jpg
│   └── metadata.json
└── aliexpress_downloader.log
```

## Example Usage

```bash
# Download from a single product
python aliexpress_image_downloader.py -u "https://www.aliexpress.com/item/**********.html"

# Download from multiple products with custom directory
python aliexpress_image_downloader.py -u "url1" "url2" -d "product_images"

# Download from URL file with verbose logging
python aliexpress_image_downloader.py -f product_urls.txt -v

# High-performance download with 10 workers
python aliexpress_image_downloader.py -f urls.txt -w 10 -d "bulk_download"
```

## Requirements

- Python 3.7+
- Internet connection
- Required packages: requests, beautifulsoup4, tqdm, lxml, Pillow

## License

This tool is for educational and personal use only. Please respect AliExpress's terms of service when using this application.
