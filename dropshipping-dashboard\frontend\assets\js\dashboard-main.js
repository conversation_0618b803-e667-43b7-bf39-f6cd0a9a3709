/**
 * Main Dashboard JavaScript
 * Handles product display, selection, and bulk operations
 */

class DashboardMain {
    constructor() {
        this.selectedProducts = new Set();
        this.currentPage = 1;
        this.totalPages = 1;
        this.products = [];
        this.filteredProducts = [];
        this.searchQuery = '';
        this.init();
    }

    async init() {
        await this.loadStats();
        await this.loadProducts();
        this.setupEventListeners();
    }

    async loadStats() {
        try {
            const response = await fetch(`${window.app.API_URL}/dashboard/stats`);
            const result = await response.json();
            
            if (result.success) {
                const stats = result.data;
                document.getElementById('ready-products').textContent = stats.ready_products || 0;
                document.getElementById('total-orders').textContent = stats.total_orders || 0;
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }

    async loadProducts(page = 1) {
        try {
            window.app.showLoading();
            
            // Products are already sorted by most recent first in the backend
            const response = await fetch(`${window.app.API_URL}/products/?page=${page}&limit=20`);
            const result = await response.json();
            
            if (result.success) {
                this.products = result.data.products;
                this.filteredProducts = [...this.products];
                this.currentPage = result.data.pagination.page;
                this.totalPages = result.data.pagination.total_pages;

                this.applySearch();
                this.renderProducts();
                this.renderPagination();

                document.getElementById('loading').style.display = 'none';
                document.getElementById('products-grid').style.display = 'grid';
                document.getElementById('pagination').style.display = 'flex';
            } else {
                throw new Error(result.error || 'Failed to load products');
            }
        } catch (error) {
            console.error('Error loading products:', error);
            window.app.showToast('Failed to load products: ' + error.message, 'error');
        } finally {
            window.app.hideLoading();
        }
    }

    renderProducts() {
        const grid = document.getElementById('products-grid');
        grid.innerHTML = '';

        if (this.filteredProducts.length === 0) {
            grid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: var(--gray-500);">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">📦</div>
                    <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 0.5rem;">No products found</div>
                    <div style="font-size: 0.875rem;">Try adjusting your search or upload new data</div>
                </div>
            `;
            return;
        }

        this.filteredProducts.forEach(product => {
            const card = this.createProductCard(product);
            grid.appendChild(card);
        });
    }

    applySearch() {
        if (!this.searchQuery.trim()) {
            this.filteredProducts = [...this.products];
            return;
        }

        const query = this.searchQuery.toLowerCase();
        this.filteredProducts = this.products.filter(product => {
            const title = (product.title || '').toLowerCase();
            const orderId = (product.order_info?.order_id || '').toLowerCase();
            const price = (product.original_price || product.final_price || '').toString();

            return title.includes(query) ||
                   orderId.includes(query) ||
                   price.includes(query);
        });
    }

    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.dataset.productId = product._id;

        // Get first image or placeholder - fix the image URL extraction
        let imageUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTJhNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';

        if (product.images && product.images.length > 0) {
            // Handle both string URLs and object format
            if (typeof product.images[0] === 'string') {
                imageUrl = product.images[0];
            } else if (product.images[0] && product.images[0].url) {
                // Handle different URL formats for extracted images
                if (product.images[0].url.startsWith('/api/')) {
                    // Remove the leading /api/ and add the full API URL
                    const pathWithoutApi = product.images[0].url.substring(4); // Remove '/api'
                    imageUrl = `${window.app.API_URL}${pathWithoutApi}`;
                } else if (product.images[0].url.startsWith('http')) {
                    imageUrl = product.images[0].url;
                } else {
                    // Assume it's a filename and construct API URL
                    imageUrl = `${window.app.API_URL}/images/images/${product.images[0].url}`;
                }
            }
        }

        // Format price - fix price extraction
        let price = 'Price not set';
        if (product.original_price) {
            price = `$${parseFloat(product.original_price).toFixed(2)}`;
        } else if (product.final_price) {
            price = `$${parseFloat(product.final_price).toFixed(2)}`;
        }

        // Format title
        const title = product.title || 'Untitled Product';

        // Order info - fix order data extraction
        const orderDate = product.order_info?.order_date ? new Date(product.order_info.order_date).toLocaleDateString() : 'N/A';
        const orderNumber = product.order_info?.order_id || 'N/A';

        // Image count
        const imageCount = product.images ? product.images.length : 0;
        const hasAliExpressUrl = product.aliexpress_data?.original_url;

        card.innerHTML = `
            <input type="checkbox" class="product-checkbox" data-product-id="${product._id}">
            <img src="${imageUrl}" alt="${title}" class="product-image" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTJhNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
            <div class="product-content">
                <h3 class="product-title">${title}</h3>
                <div class="product-price">${price}</div>
                <div class="product-meta">
                    <span>Order: ${orderNumber}</span>
                    <span>${orderDate}</span>
                    <span>📸 ${imageCount} image${imageCount !== 1 ? 's' : ''}</span>
                </div>
                <div class="product-actions">
                    <button class="add-btn" data-product-id="${product._id}">Add to Workspace</button>
                </div>
            </div>
        `;

        return card;
    }

    renderPagination() {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        if (this.totalPages <= 1) return;

        // First button
        if (this.currentPage > 1) {
            const firstBtn = document.createElement('button');
            firstBtn.className = 'page-btn';
            firstBtn.textContent = '« First';
            firstBtn.onclick = () => this.loadProducts(1);
            pagination.appendChild(firstBtn);
        }

        // Previous button
        if (this.currentPage > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '‹ Prev';
            prevBtn.onclick = () => this.loadProducts(this.currentPage - 1);
            pagination.appendChild(prevBtn);
        }

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.loadProducts(i);
            pagination.appendChild(pageBtn);
        }

        // Next button
        if (this.currentPage < this.totalPages) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = 'Next ›';
            nextBtn.onclick = () => this.loadProducts(this.currentPage + 1);
            pagination.appendChild(nextBtn);
        }

        // Last button
        if (this.currentPage < this.totalPages) {
            const lastBtn = document.createElement('button');
            lastBtn.className = 'page-btn';
            lastBtn.textContent = 'Last »';
            lastBtn.onclick = () => this.loadProducts(this.totalPages);
            pagination.appendChild(lastBtn);
        }
    }

    setupEventListeners() {
        // Product selection
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('product-checkbox')) {
                this.handleProductSelection(e.target);
            }
        });

        // Remove individual extract button listener since we're doing bulk processing

        // Individual add buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-btn')) {
                const productId = e.target.dataset.productId;
                this.addToWorkspace([productId]);
            }
        });

        // Product card clicks (for selection)
        document.addEventListener('click', (e) => {
            const card = e.target.closest('.product-card');
            if (card && !e.target.classList.contains('add-btn') && !e.target.classList.contains('product-checkbox')) {
                const checkbox = card.querySelector('.product-checkbox');
                checkbox.checked = !checkbox.checked;
                this.handleProductSelection(checkbox);
            }
        });

        // Select all button
        document.getElementById('select-all-btn').addEventListener('click', () => {
            this.toggleSelectAll();
        });

        // Bulk add button - now includes image extraction and Shopify redirect
        document.getElementById('bulk-add-btn').addEventListener('click', () => {
            if (this.selectedProducts.size > 0) {
                this.processSelectedProducts(Array.from(this.selectedProducts));
            } else {
                window.app.showToast('Please select products first', 'warning');
            }
        });

        // Search functionality
        const searchInput = document.getElementById('search-input');
        searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.applySearch();
            this.renderProducts();
            this.updateSelectionUI();
        });
    }

    handleProductSelection(checkbox) {
        const productId = checkbox.dataset.productId;
        const card = checkbox.closest('.product-card');

        if (checkbox.checked) {
            this.selectedProducts.add(productId);
            card.classList.add('selected');
        } else {
            this.selectedProducts.delete(productId);
            card.classList.remove('selected');
        }

        this.updateSelectionUI();
    }

    updateSelectionUI() {
        const count = this.selectedProducts.size;
        document.getElementById('selected-count').textContent = `${count} selected`;
        document.getElementById('selected-products').textContent = count;

        // Update select all button
        const selectAllBtn = document.getElementById('select-all-btn');
        const allCheckboxes = document.querySelectorAll('.product-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.product-checkbox:checked');

        if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
            selectAllBtn.textContent = 'Deselect All';
        } else {
            selectAllBtn.textContent = 'Select All';
        }
    }

    toggleSelectAll() {
        const allCheckboxes = document.querySelectorAll('.product-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
        const shouldSelectAll = checkedCheckboxes.length !== allCheckboxes.length;

        allCheckboxes.forEach(checkbox => {
            checkbox.checked = shouldSelectAll;
            this.handleProductSelection(checkbox);
        });
    }

    async processSelectedProducts(productIds) {
        try {
            // Safety check for large bulk operations
            if (productIds.length > 100) {
                const confirmed = confirm(`You're about to process ${productIds.length} products. This may take a long time and could impact server performance. Continue?`);
                if (!confirmed) {
                    return;
                }
            } else if (productIds.length > 50) {
                const confirmed = confirm(`Processing ${productIds.length} products may take several minutes. Continue?`);
                if (!confirmed) {
                    return;
                }
            }

            // Show progress modal
            this.showProgressModal('Processing Selected Products', productIds.length);

            let completed = 0;
            let errors = [];

            // Step 1: Extract images for all selected products
            this.updateProgress('Extracting images...', 0, productIds.length);

            // Process in smaller batches to prevent server overload
            const batchSize = Math.min(5, productIds.length); // Max 5 concurrent
            const batches = [];
            for (let i = 0; i < productIds.length; i += batchSize) {
                batches.push(productIds.slice(i, i + batchSize));
            }

            for (const batch of batches) {
                // Process batch concurrently
                const batchPromises = batch.map(async (productId) => {
                    try {
                        const response = await fetch(`${window.app.API_URL}/images/extract/${productId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (!result.success) {
                            errors.push(`Failed to extract images for product ${productId}: ${result.error}`);
                        }

                        return { success: true, productId };
                    } catch (error) {
                        errors.push(`Error processing product ${productId}: ${error.message}`);
                        return { success: false, productId, error: error.message };
                    }
                });

                // Wait for batch to complete
                await Promise.all(batchPromises);

                completed += batch.length;
                this.updateProgress(`Extracting images... (${completed}/${productIds.length})`, completed, productIds.length);

                // Delay between batches to prevent overwhelming server
                if (batches.indexOf(batch) < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second between batches
                }
            }

            // Step 2: Add to workspace
            this.updateProgress('Adding to workspace...', completed, productIds.length + 1);

            const workspaceResponse = await fetch(`${window.app.API_URL}/workspace/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    product_ids: productIds
                })
            });

            const workspaceResult = await workspaceResponse.json();

            if (workspaceResult.success) {
                this.updateProgress('Complete! Redirecting to Shopify preparation...', productIds.length + 1, productIds.length + 1);

                // Show success message
                if (errors.length > 0) {
                    window.app.showToast(`Processed ${productIds.length} products with ${errors.length} warnings. Check console for details.`, 'warning');
                    console.warn('Processing warnings:', errors);
                } else {
                    window.app.showToast(`Successfully processed ${productIds.length} products!`, 'success');
                }

                // Wait a moment then redirect
                setTimeout(() => {
                    this.hideProgressModal();
                    window.location.href = 'workspace.html';
                }, 1500);

            } else {
                throw new Error(workspaceResult.error || 'Failed to add products to workspace');
            }

        } catch (error) {
            console.error('Error processing selected products:', error);
            window.app.showToast(`Failed to process products: ${error.message}`, 'error');
            this.hideProgressModal();
        }
    }

    showProgressModal(title, totalSteps) {
        // Create progress modal if it doesn't exist
        let modal = document.getElementById('progress-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'progress-modal';
            modal.className = 'progress-modal';
            modal.innerHTML = `
                <div class="progress-modal-content">
                    <h3 id="progress-title">${title}</h3>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-bar"></div>
                    </div>
                    <p id="progress-text">Initializing...</p>
                    <div id="progress-details"></div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        modal.style.display = 'flex';
        document.getElementById('progress-title').textContent = title;
        this.updateProgress('Initializing...', 0, totalSteps);
    }

    updateProgress(text, current, total) {
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');

        if (progressBar && progressText) {
            const percentage = total > 0 ? (current / total) * 100 : 0;
            progressBar.style.width = `${percentage}%`;
            progressText.textContent = text;
        }
    }

    hideProgressModal() {
        const modal = document.getElementById('progress-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    async addToWorkspace(productIds) {
        try {
            window.app.showLoading();

            // Step 1: Extract images first (same as bulk processing)
            console.log(`Starting image extraction for products: ${productIds.join(', ')}`);
            let errors = [];
            for (const productId of productIds) {
                try {
                    console.log(`Extracting images for product: ${productId}`);
                    const response = await fetch(`${window.app.API_URL}/images/extract/${productId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();
                    console.log(`Extraction result for ${productId}:`, result);

                    if (!result.success) {
                        errors.push(`Failed to extract images for product ${productId}: ${result.error}`);
                    }

                } catch (error) {
                    console.error(`Error extracting images for ${productId}:`, error);
                    errors.push(`Error processing product ${productId}: ${error.message}`);
                }
            }

            // Step 2: Add to workspace
            const response = await fetch(`${window.app.API_URL}/workspace/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    product_ids: productIds
                })
            });

            const result = await response.json();

            if (result.success) {
                const count = productIds.length;
                if (errors.length > 0) {
                    window.app.showToast(`Added ${count} product${count > 1 ? 's' : ''} to workspace with ${errors.length} image extraction warnings.`, 'warning');
                    console.warn('Image extraction warnings:', errors);
                } else {
                    window.app.showToast(`Added ${count} product${count > 1 ? 's' : ''} to workspace with images extracted!`, 'success');
                }

                // Clear selection
                this.selectedProducts.clear();
                document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
                    checkbox.checked = false;
                    checkbox.closest('.product-card').classList.remove('selected');
                });
                this.updateSelectionUI();

                // Update stats
                await this.loadStats();
            } else {
                throw new Error(result.error || 'Failed to add products to workspace');
            }
        } catch (error) {
            console.error('Error adding to workspace:', error);
            window.app.showToast('Failed to add products: ' + error.message, 'error');
        } finally {
            window.app.hideLoading();
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DashboardMain();
});
