"""
Image processing routes for background removal and image management
"""
from flask import Blueprint, request, jsonify, send_file
from services.background_removal_service import BackgroundRemovalService
from services.image_service import ImageService
from services.mongodb_service import mongodb_service
import logging
import threading
import os

logger = logging.getLogger(__name__)

image_processing_bp = Blueprint('image_processing', __name__)
bg_removal_service = BackgroundRemovalService()
image_service = ImageService()

# Track active extractions for monitoring
active_extractions = threading.Lock()
extraction_count = 0

@image_processing_bp.route('/status', methods=['GET'])
def get_extraction_status():
    """Get current extraction status for monitoring"""
    global extraction_count
    with active_extractions:
        return jsonify({
            'success': True,
            'data': {
                'active_extractions': extraction_count,
                'server_status': 'healthy' if extraction_count < 10 else 'busy'
            }
        })

@image_processing_bp.route('/fix-image-urls', methods=['POST'])
def fix_image_urls():
    """Fix products with old image URL formats"""
    try:
        # Find products with old URL format
        products = mongodb_service.db.products.find({
            'images.url': {'$regex': '^extracted_from_'}
        })

        fixed_count = 0
        for product in products:
            product_id = str(product['_id'])
            images = product.get('images', [])
            updated_images = []

            for img in images:
                if img.get('url', '').startswith('extracted_from_'):
                    # Try to find the actual image file
                    local_path = img.get('local_path', '')
                    if local_path and os.path.exists(local_path):
                        # Create proper URL from local path
                        static_images_index = local_path.find('static/images/')
                        if static_images_index != -1:
                            relative_path = local_path[static_images_index + len('static/images/'):]
                            relative_path = relative_path.replace('\\', '/')
                            img['url'] = f"/api/images/images/{relative_path}"
                        else:
                            filename = os.path.basename(local_path)
                            img['url'] = f"/api/images/images/{filename}"

                updated_images.append(img)

            # Update the product
            if updated_images:
                mongodb_service.db.products.update_one(
                    {'_id': product['_id']},
                    {'$set': {'images': updated_images}}
                )
                fixed_count += 1

        return jsonify({
            'success': True,
            'data': {
                'fixed_count': fixed_count,
                'message': f'Fixed image URLs for {fixed_count} products'
            }
        })

    except Exception as e:
        logger.error(f"Error fixing image URLs: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/fix-all-image-urls', methods=['POST'])
def fix_all_image_urls():
    """Fix all products with incorrect image URL formats based on local_path"""
    try:
        # Find products with images that have local_path
        products = mongodb_service.db.products.find({
            'images': {'$exists': True, '$ne': []},
            'images.local_path': {'$exists': True}
        })

        fixed_count = 0
        for product in products:
            product_id = str(product['_id'])
            images = product.get('images', [])
            updated_images = []
            needs_update = False

            for img in images:
                local_path = img.get('local_path', '')
                current_url = img.get('url', '')

                if local_path and ('static/images' in local_path or 'static\\images' in local_path):
                    # Generate correct URL from local_path using the same logic as image_service
                    try:
                        # Normalize the path first
                        normalized_path = local_path.replace('\\', '/')

                        # Find the static/images part in the normalized path
                        static_images_index = normalized_path.find('static/images/')
                        if static_images_index != -1:
                            # Get everything after static/images/
                            relative_path = normalized_path[static_images_index + len('static/images/'):]
                            correct_url = f"/api/images/images/{relative_path}"
                        else:
                            # Try with mixed separators (static/images\)
                            mixed_index = local_path.find('static/images\\')
                            if mixed_index != -1:
                                # Get everything after static/images\
                                relative_path = local_path[mixed_index + len('static/images\\'):]
                                # Replace backslashes with forward slashes for URL
                                relative_path = relative_path.replace('\\', '/')
                                correct_url = f"/api/images/images/{relative_path}"
                            else:
                                # Keep original URL
                                correct_url = current_url

                        # Check if URL needs updating
                        if correct_url != current_url:
                            logger.info(f"Fixing URL for product {product_id}: {current_url} -> {correct_url}")
                            img['url'] = correct_url
                            needs_update = True

                        updated_images.append(img)

                    except Exception as e:
                        logger.error(f"Error processing image for product {product_id}: {e}")
                        updated_images.append(img)
                else:
                    updated_images.append(img)

            if needs_update:
                # Update the product
                mongodb_service.db.products.update_one(
                    {'_id': product['_id']},
                    {'$set': {'images': updated_images}}
                )
                fixed_count += 1

        return jsonify({
            'success': True,
            'data': {
                'fixed_count': fixed_count,
                'message': f'Fixed image URLs for {fixed_count} products'
            }
        })

    except Exception as e:
        logger.error(f"Error fixing all image URLs: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/extract/<product_id>', methods=['POST'])
def extract_images(product_id):
    """Extract images for a product using AliExpress downloader"""
    global extraction_count

    try:
        # Check server load
        with active_extractions:
            if extraction_count >= 10:  # Limit concurrent extractions
                return jsonify({
                    'success': False,
                    'error': 'Server is busy. Please try again in a moment.'
                }), 429
            extraction_count += 1

        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return jsonify({
                    'success': False,
                    'error': 'Product not found'
                }), 404

            # Start image extraction
            result = image_service.extract_product_images(product_id)

            return jsonify({
                'success': True,
                'data': result
            })

        finally:
            # Always decrement counter
            with active_extractions:
                extraction_count = max(0, extraction_count - 1)

    except Exception as e:
        logger.error(f"Error extracting images: {e}")
        with active_extractions:
            extraction_count = max(0, extraction_count - 1)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/bulk-extract', methods=['POST'])
def bulk_extract_images():
    """Bulk extract images for multiple products"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        
        # Start bulk extraction
        result = image_service.bulk_extract_images(product_ids)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"Error in bulk image extraction: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/remove-background/<product_id>', methods=['POST'])
def remove_background(product_id):
    """Remove background from product images"""
    try:
        data = request.get_json() or {}
        provider = data.get('provider', 'cloudinary')  # Default to Cloudinary
        image_indices = data.get('image_indices', [])  # Which images to process

        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404

        # Process background removal
        result = bg_removal_service.remove_background_for_product(
            product_id, provider, image_indices
        )

        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"Error removing background: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/bulk-remove-background', methods=['POST'])
def bulk_remove_background():
    """Bulk remove background for multiple products"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        provider = data.get('provider', 'remove.bg')
        
        # Start bulk processing
        result = bg_removal_service.bulk_remove_background(product_ids, provider)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"Error in bulk background removal: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/set-main/<product_id>/<int:image_index>', methods=['POST'])
def set_main_image(product_id, image_index):
    """Set main product image"""
    try:
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        images = product.get('images', [])
        if image_index >= len(images):
            return jsonify({
                'success': False,
                'error': 'Invalid image index'
            }), 400
        
        # Update image selection
        for i, image in enumerate(images):
            image['is_main'] = (i == image_index)
            image['is_selected_by_user'] = (i == image_index)
        
        # Update product
        success = mongodb_service.update_product(product_id, {'images': images})
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Main image updated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update main image'
            }), 500
        
    except Exception as e:
        logger.error(f"Error setting main image: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/reorder/<product_id>', methods=['POST'])
def reorder_images(product_id):
    """Reorder product images"""
    try:
        data = request.get_json()
        
        if not data or 'image_order' not in data:
            return jsonify({
                'success': False,
                'error': 'image_order is required'
            }), 400
        
        new_order = data['image_order']  # List of indices in new order
        
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        images = product.get('images', [])
        
        # Validate new order
        if len(new_order) != len(images) or set(new_order) != set(range(len(images))):
            return jsonify({
                'success': False,
                'error': 'Invalid image order'
            }), 400
        
        # Reorder images
        reordered_images = [images[i] for i in new_order]
        
        # Update product
        success = mongodb_service.update_product(product_id, {'images': reordered_images})
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Images reordered successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to reorder images'
            }), 500
        
    except Exception as e:
        logger.error(f"Error reordering images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/preview/<product_id>')
def get_image_preview(product_id):
    """Get image previews for a product"""
    try:
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        images = product.get('images', [])
        
        # Prepare preview data
        preview_data = []
        for i, image in enumerate(images):
            preview_data.append({
                'index': i,
                'url': image.get('url', ''),
                'local_path': image.get('local_path', ''),
                'processed_path': image.get('processed_path', ''),
                'is_main': image.get('is_main', False),
                'is_selected_by_user': image.get('is_selected_by_user', False),
                'download_status': image.get('download_status', 'pending'),
                'background_removal': image.get('background_removal', {})
            })
        
        return jsonify({
            'success': True,
            'data': {
                'product_id': product_id,
                'product_title': product.get('title', ''),
                'images': preview_data
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting image preview: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/status/<product_id>')
def get_processing_status(product_id):
    """Get image processing status for a product"""
    try:
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        images = product.get('images', [])
        
        # Calculate status summary
        total_images = len(images)
        downloaded_images = sum(1 for img in images if img.get('download_status') == 'completed')
        processed_images = sum(1 for img in images if img.get('background_removal', {}).get('status') == 'completed')
        failed_images = sum(1 for img in images if img.get('download_status') == 'failed' or img.get('background_removal', {}).get('status') == 'failed')
        
        status_summary = {
            'total_images': total_images,
            'downloaded_images': downloaded_images,
            'processed_images': processed_images,
            'failed_images': failed_images,
            'download_progress': (downloaded_images / total_images * 100) if total_images > 0 else 0,
            'processing_progress': (processed_images / total_images * 100) if total_images > 0 else 0
        }
        
        return jsonify({
            'success': True,
            'data': status_summary
        })
        
    except Exception as e:
        logger.error(f"Error getting processing status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/retry/<product_id>', methods=['POST'])
def retry_failed_processing(product_id):
    """Retry failed image processing"""
    try:
        data = request.get_json() or {}
        operation = data.get('operation', 'both')  # download, background_removal, or both
        
        # Get product
        product = mongodb_service.get_product(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        results = {
            'success': True,
            'retried_operations': [],
            'errors': []
        }
        
        # Retry download if requested
        if operation in ['download', 'both']:
            try:
                download_result = image_service.retry_failed_downloads(product_id)
                results['retried_operations'].append('download')
                results['download_result'] = download_result
            except Exception as e:
                results['errors'].append(f"Download retry failed: {str(e)}")
        
        # Retry background removal if requested
        if operation in ['background_removal', 'both']:
            try:
                bg_result = bg_removal_service.retry_failed_processing(product_id)
                results['retried_operations'].append('background_removal')
                results['background_removal_result'] = bg_result
            except Exception as e:
                results['errors'].append(f"Background removal retry failed: {str(e)}")
        
        return jsonify({
            'success': True,
            'data': results
        })
        
    except Exception as e:
        logger.error(f"Error retrying processing: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/estimate-cost', methods=['POST'])
def estimate_processing_cost():
    """Estimate cost for image processing"""
    try:
        data = request.get_json()
        
        if not data or 'product_ids' not in data:
            return jsonify({
                'success': False,
                'error': 'product_ids is required'
            }), 400
        
        product_ids = data['product_ids']
        provider = data.get('provider', 'remove.bg')
        
        # Calculate cost estimate
        total_images = 0
        for product_id in product_ids:
            product = mongodb_service.get_product(product_id)
            if product:
                total_images += len(product.get('images', []))
        
        cost_estimate = bg_removal_service.estimate_cost(total_images, provider)
        
        return jsonify({
            'success': True,
            'data': {
                'total_products': len(product_ids),
                'total_images': total_images,
                'provider': provider,
                'estimated_cost': cost_estimate,
                'cost_per_image': cost_estimate / total_images if total_images > 0 else 0
            }
        })
        
    except Exception as e:
        logger.error(f"Error estimating cost: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/providers')
def get_bg_removal_providers():
    """Get available background removal providers"""
    try:
        providers = bg_removal_service.get_available_providers()
        
        return jsonify({
            'success': True,
            'data': providers
        })
        
    except Exception as e:
        logger.error(f"Error getting providers: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@image_processing_bp.route('/images/<path:image_path>', methods=['GET'])
def serve_image(image_path):
    """Serve images from local storage"""
    try:
        import os
        from urllib.parse import unquote
        from config import Config

        # Decode the URL-encoded path
        decoded_path = unquote(image_path)

        # Get the full path to the image
        config = Config()
        full_path = os.path.join(config.IMAGE_STORAGE_PATH, decoded_path)

        # Security check: ensure the path is within the storage directory
        storage_path = os.path.abspath(config.IMAGE_STORAGE_PATH)
        requested_path = os.path.abspath(full_path)

        if not requested_path.startswith(storage_path):
            return jsonify({
                'success': False,
                'error': 'Invalid image path'
            }), 403

        # Check if file exists
        if not os.path.exists(full_path):
            return jsonify({
                'success': False,
                'error': 'Image not found'
            }), 404

        # Serve the file
        return send_file(full_path)

    except Exception as e:
        logger.error(f"Error serving image {image_path}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500