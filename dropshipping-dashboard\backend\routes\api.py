"""
Main API routes for Dropshipping Dashboard
"""
from flask import Blueprint, jsonify, request
from services.mongodb_service import mongodb_service
from services.cloudinary_service import cloudinary_service
import logging

logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

@api_bp.route('/')
def api_root():
    """Root endpoint for API"""
    return jsonify({
        'message': 'Dropshipping Dashboard API',
        'version': '1.0.0',
        'status': 'running'
    })

@api_bp.route('/dashboard/stats')
def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        stats = mongodb_service.get_dashboard_stats()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/search')
def search():
    """Global search endpoint"""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({
                'success': False,
                'error': 'Search query is required'
            }), 400
        
        limit = min(int(request.args.get('limit', 50)), 100)
        
        # Search products
        products = mongodb_service.search_products(query, limit)
        
        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'total': len(products)
            }
        })
    except Exception as e:
        logger.error(f"Error in search: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/config')
def get_config():
    """Get application configuration"""
    try:
        # Return safe configuration data (no sensitive info)
        config_data = {
            'max_file_size': 100 * 1024 * 1024,  # 100MB
            'allowed_extensions': ['json'],
            'default_llm_provider': 'openai',
            'default_bg_removal_provider': 'remove.bg',
            'supported_llm_providers': ['openai', 'claude', 'gemini'],
            'supported_bg_removal_providers': ['remove.bg', 'photoroom', 'clipdrop']
        }
        
        return jsonify({
            'success': True,
            'data': config_data
        })
    except Exception as e:
        logger.error(f"Error getting config: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/test-connection')
def test_connection():
    """Test database connection"""
    try:
        stats = mongodb_service.get_dashboard_stats()
        return jsonify({
            'success': True,
            'message': 'Database connection successful',
            'stats': stats
        })
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return jsonify({
            'success': False,
            'error': 'Database connection failed',
            'details': str(e)
        }), 500

@api_bp.route('/database/clear', methods=['POST'])
def clear_database():
    """Clear all data from database and Cloudinary - USE WITH CAUTION"""
    try:
        # Delete ALL images from Cloudinary first
        logger.warning("🚨 NUCLEAR OPTION: Deleting ALL images from Cloudinary")
        cloudinary_result = cloudinary_service.delete_all_images()
        
        if cloudinary_result['success']:
            logger.info(f"Cloudinary cleanup: {cloudinary_result['deleted_count']} images deleted")
        else:
            logger.error(f"Cloudinary cleanup failed: {cloudinary_result['error']}")
        
        # Clear all collections
        products_deleted = mongodb_service.db.products.delete_many({}).deleted_count
        orders_deleted = mongodb_service.db.orders.delete_many({}).deleted_count
        batches_deleted = mongodb_service.db.import_batches.delete_many({}).deleted_count

        logger.info(f"Database cleared: {products_deleted} products, {orders_deleted} orders, {batches_deleted} batches")

        return jsonify({
            'success': True,
            'data': {
                'products_deleted': products_deleted,
                'orders_deleted': orders_deleted,
                'batches_deleted': batches_deleted,
                'cloudinary_images_deleted': cloudinary_result.get('deleted_count', 0),
                'cloudinary_errors': cloudinary_result.get('errors', [])
            }
        })

    except Exception as e:
        logger.error(f"Error clearing database: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/clear-all-data', methods=['POST'])
def clear_all_data():
    """Clear all data from database and Cloudinary - USE WITH CAUTION"""
    try:
        # Require confirmation
        data = request.get_json()
        if not data or data.get('confirm') != 'yes':
            return jsonify({
                'success': False,
                'error': 'Confirmation required. Send {"confirm": "yes"} to proceed.'
            }), 400

        # Delete ALL images from Cloudinary first
        logger.warning("🚨 NUCLEAR OPTION: Deleting ALL images from Cloudinary")
        cloudinary_result = cloudinary_service.delete_all_images()
        
        if cloudinary_result['success']:
            logger.info(f"Cloudinary cleanup: {cloudinary_result['deleted_count']} images deleted")
        else:
            logger.error(f"Cloudinary cleanup failed: {cloudinary_result['error']}")

        # Clear products collection
        products_result = mongodb_service.db.products.delete_many({})

        # Clear orders collection
        orders_result = mongodb_service.db.orders.delete_many({})

        # Clear import batches collection
        batches_result = mongodb_service.db.import_batches.delete_many({})

        return jsonify({
            'success': True,
            'message': 'All data and images cleared successfully',
            'deleted': {
                'products': products_result.deleted_count,
                'orders': orders_result.deleted_count,
                'import_batches': batches_result.deleted_count,
                'cloudinary_images': cloudinary_result.get('deleted_count', 0)
            },
            'cloudinary_errors': cloudinary_result.get('errors', [])
        })

    except Exception as e:
        logger.error(f"Error clearing data: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500