#!/usr/bin/env python3
"""
Test script to verify image extraction functionality
"""
import sys
import os
sys.path.append('dropshipping-dashboard/backend')

from services.mongodb_service import mongodb_service
from services.image_service import ImageService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("🧪 Dropshipping Dashboard - Image Extraction Test")
    print("=" * 60)
    
    # Test 1: Check database connection
    print("\n1. Testing Database Connection...")
    try:
        stats = mongodb_service.get_dashboard_stats()
        print(f"✅ Database connected successfully")
        print(f"   - Total Products: {stats.get('total_products', 0)}")
        print(f"   - Total Orders: {stats.get('total_orders', 0)}")
        print(f"   - Import Batches: {stats.get('total_import_batches', 0)}")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return
    
    # Test 2: Get sample product
    print("\n2. Getting Sample Product...")
    try:
        products = mongodb_service.get_products(limit=1)
        if not products:
            print("❌ No products found in database")
            return
        
        product = products[0]
        print(f"✅ Found sample product:")
        print(f"   - ID: {product['_id']}")
        print(f"   - Title: {product.get('title', 'N/A')}")
        print(f"   - Status: {product.get('status', 'N/A')}")
        print(f"   - Current Images: {len(product.get('images', []))} images")
        
        # Check AliExpress URL
        aliexpress_url = product.get('aliexpress_data', {}).get('original_url', '')
        print(f"   - AliExpress URL: {aliexpress_url}")
        
        if not aliexpress_url:
            print("❌ No AliExpress URL found")
            return
            
    except Exception as e:
        print(f"❌ Error getting product: {e}")
        return
    
    # Test 3: Check AliExpress downloader
    print("\n3. Testing AliExpress Downloader Path...")
    try:
        image_service = ImageService()
        downloader_path = image_service.downloader_path
        print(f"   - Downloader path: {downloader_path}")
        print(f"   - File exists: {os.path.exists(downloader_path)}")
        
        if not os.path.exists(downloader_path):
            print("❌ AliExpress downloader not found!")
            # Try to find it
            possible_paths = [
                'aliexpress_image_downloader.py',
                '../aliexpress_image_downloader.py',
                '../../aliexpress_image_downloader.py'
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    print(f"   - Found downloader at: {path}")
                    break
            return
        else:
            print("✅ AliExpress downloader found")
            
    except Exception as e:
        print(f"❌ Error checking downloader: {e}")
        return
    
    # Test 4: Test URL formatting
    print("\n4. Testing URL Formatting...")
    try:
        # Test protocol-relative URL fix
        test_url = aliexpress_url
        if test_url.startswith('//'):
            fixed_url = 'https:' + test_url
            print(f"   - Original URL: {test_url}")
            print(f"   - Fixed URL: {fixed_url}")
        else:
            print(f"   - URL already formatted: {test_url}")
            
    except Exception as e:
        print(f"❌ Error testing URL: {e}")
        return
    
    # Test 5: Test image extraction (dry run)
    print("\n5. Testing Image Extraction (Dry Run)...")
    try:
        product_id = product['_id']
        print(f"   - Testing extraction for product: {product_id}")
        
        # This will test the extraction without actually running it
        result = image_service.extract_product_images(product_id)
        
        if result.get('success'):
            print("✅ Image extraction test successful")
            print(f"   - Result: {result}")
        else:
            print(f"❌ Image extraction failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing extraction: {e}")
        return
    
    print("\n" + "=" * 60)
    print("🎉 Test completed!")

if __name__ == "__main__":
    main()
