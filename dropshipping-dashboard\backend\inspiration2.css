import React from "react";
import styled from "styled-components";

const StyledRectangle1 = styled.div`
  width: 1440px;
  height: 932px;
  background: white;
  box-shadow: 0px 44px 84px 6px #D8D9DB;
  border-radius: 30px;
`;

const StyledVector = styled.div`
  width: 6.92px;
  height: 6.91px;
  left: 2.54px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector01 = styled.div`
  width: 3.39px;
  height: 5.67px;
  left: 10.38px;
  top: 2.17px;
  position: absolute;
  background: #787486;
`;

const StyledVector02 = styled.div`
  width: 10.09px;
  height: 6.74px;
  left: 1.07px;
  top: 8.29px;
  position: absolute;
  background: #787486;
`;

const StyledVector03 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector04 = styled.div`
  width: 3.09px;
  height: 5.01px;
  left: 11.73px;
  top: 8.83px;
  position: absolute;
  background: #787486;
`;

const StyledRectangle1062 = styled.div`
  width: 97px;
  height: 40px;
  left: 0px;
  top: 0px;
  position: absolute;
  border-radius: 6px;
  border: 1px #787486 solid;
`;

const StyledSharespan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  text-transform: capitalize;
  word-wrap: break-word;
`;

const StyledRectangle1064 = styled.div`
  width: 40px;
  height: 40px;
  left: 137px;
  top: 0px;
  position: absolute;
  background: #5030E5;
  border-radius: 6px;
`;

const StyledVector05 = styled.div`
  width: 15px;
  height: 6.38px;
  left: 2.50px;
  top: 11.13px;
  position: absolute;
  background: white;
`;

const StyledVector06 = styled.div`
  width: 15px;
  height: 6.37px;
  left: 2.50px;
  top: 2.50px;
  position: absolute;
  background: white;
`;

const StyledVector07 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector08 = styled.div`
  width: 5px;
  height: 5px;
  left: 12.69px;
  top: 3px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector09 = styled.div`
  width: 5px;
  height: 5px;
  left: 3px;
  top: 3px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector10 = styled.div`
  width: 5px;
  height: 5px;
  left: 12.69px;
  top: 13px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector11 = styled.div`
  width: 5px;
  height: 5px;
  left: 3px;
  top: 13px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector12 = styled.div`
  width: 21px;
  height: 21px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector13 = styled.div`
  width: 9.50px;
  height: 9.50px;
  left: 1.25px;
  top: 1.25px;
  position: absolute;
  background: #787486;
`;

const StyledVector14 = styled.div`
  width: 9.50px;
  height: 9.50px;
  left: 13.25px;
  top: 1.25px;
  position: absolute;
  background: #787486;
`;

const StyledVector15 = styled.div`
  width: 9.50px;
  height: 9.50px;
  left: 13.25px;
  top: 13.25px;
  position: absolute;
  background: #787486;
`;

const StyledVector16 = styled.div`
  width: 9.50px;
  height: 9.50px;
  left: 1.25px;
  top: 13.25px;
  position: absolute;
  background: #787486;
`;

const StyledVector17 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledHomespan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledVector19 = styled.div`
  width: 3px;
  height: 2.25px;
  left: 6.38px;
  top: 7.38px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector21 = styled.div`
  width: 3px;
  height: 2.25px;
  left: 6.38px;
  top: 14.38px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector22 = styled.div`
  width: 20px;
  height: 20px;
  left: 2px;
  top: 2px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector23 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledTasksspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledVector24 = styled.div`
  width: 20px;
  height: 20.06px;
  left: 2px;
  top: 2px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector25 = styled.div`
  width: 1px;
  height: 1px;
  left: 15.50px;
  top: 10.50px;
  position: absolute;
  outline: 2px #787486 solid;
  outline-offset: -1px;
`;

const StyledVector26 = styled.div`
  width: 1px;
  height: 1px;
  left: 11.50px;
  top: 10.50px;
  position: absolute;
  outline: 2px #787486 solid;
  outline-offset: -1px;
`;

const StyledVector27 = styled.div`
  width: 1px;
  height: 1px;
  left: 7.50px;
  top: 10.50px;
  position: absolute;
  outline: 2px #787486 solid;
  outline-offset: -1px;
`;

const StyledVector28 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledMessagesspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledVector29 = styled.div`
  width: 8.88px;
  height: 8.87px;
  left: 4.56px;
  top: 2px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector30 = styled.div`
  width: 3.63px;
  height: 7px;
  left: 16.28px;
  top: 4px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector31 = styled.div`
  width: 13.64px;
  height: 8.62px;
  left: 2.34px;
  top: 13.19px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector32 = styled.div`
  width: 3.13px;
  height: 6px;
  left: 18.34px;
  top: 14px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector33 = styled.div`
  width: 24px;
  height: 24px;
  left: 24px;
  top: 24px;
  position: absolute;
  transform: rotate(-180deg);
  transform-origin: top left;
  opacity: 0;
`;

const StyledMembersspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledVector34 = styled.div`
  width: 6px;
  height: 6px;
  left: 9px;
  top: 9px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector35 = styled.div`
  width: 20.01px;
  height: 18.88px;
  left: 2px;
  top: 2.56px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector36 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledSettingsspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledVector39 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector40 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledMyprojectsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 700;
  text-transform: uppercase;
  word-wrap: break-word;
`;

const StyledRectangle1056 = styled.div`
  width: 225px;
  height: 39px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: rgba(80, 48, 229, 0.08);
  border-radius: 6px;
`;

const StyledMobileappspan = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const StyledSpan = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledEllipse8 = styled.div`
  width: 8px;
  height: 8px;
  left: 12px;
  top: 15px;
  position: absolute;
  background: #7AC555;
  border-radius: 9999px;
`;

const StyledWebsiteredesignspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse9 = styled.div`
  width: 8px;
  height: 8px;
  left: 0px;
  top: 5px;
  position: absolute;
  background: #FFA500;
  border-radius: 9999px;
`;

const StyledDesignsystemspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse10 = styled.div`
  width: 8px;
  height: 8px;
  left: 0px;
  top: 5px;
  position: absolute;
  background: #E4CCFD;
  border-radius: 9999px;
`;

const StyledWireframesspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse11 = styled.div`
  width: 8px;
  height: 8px;
  left: 0px;
  top: 5px;
  position: absolute;
  background: #76A5EA;
  border-radius: 9999px;
`;

const StyledVector41 = styled.div`
  width: 12px;
  height: 11.79px;
  left: 2px;
  top: 10.21px;
  position: absolute;
  opacity: 0.60;
  background: #5030E5;
`;

const StyledVector42 = styled.div`
  width: 12px;
  height: 12px;
  left: 6px;
  top: 2px;
  position: absolute;
  background: #5030E5;
`;

const StyledVector43 = styled.div`
  width: 10px;
  height: 11.79px;
  left: 12px;
  top: 10.21px;
  position: absolute;
  opacity: 0.40;
  background: #5030E5;
`;

const StyledVector44 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector45 = styled.div`
  width: 7.16px;
  height: 14.45px;
  left: 5.96px;
  top: 2.78px;
  position: absolute;
  background: #787486;
`;

const StyledVector46 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector47 = styled.div`
  width: 7.16px;
  height: 14.45px;
  left: 5.96px;
  top: 2.78px;
  position: absolute;
  background: #787486;
`;

const StyledVector48 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledProjectmspan = styled.span`
  color: #0D062D;
  font-size: 20px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const StyledRectangle1057 = styled.div`
  width: 354px;
  height: 625px;
  background: #F5F5F5;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
`;

const StyledRectangle1066 = styled.div`
  width: 314px;
  height: 177px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledVector49 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector50 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 5.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector51 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector53 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 0.83px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector54 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.33px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector55 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector56 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 4.67px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector57 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const Styled12commentsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled0filesspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse15 = styled.div`
  width: 24px;
  height: 24px;
  left: 59px;
  top: 133px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse13 = styled.div`
  width: 25px;
  height: 24px;
  left: 39px;
  top: 133px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse12 = styled.div`
  width: 24px;
  height: 24px;
  left: 20px;
  top: 133px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledBrainstormingspan = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled01span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledBrainstormingbringsteammembersdiverseexperienceintoplayspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledRectangle1067 = styled.div`
  width: 36px;
  height: 23px;
  left: 20px;
  top: 20px;
  position: absolute;
  background: rgba(223, 168, 116, 0.20);
  border-radius: 4px;
`;

const StyledLowspan = styled.span`
  color: #D58D49;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle106601 = styled.div`
  width: 323.81px;
  height: 211.76px;
  transform: rotate(3deg);
  transform-origin: top left;
  background: rgba(196, 196, 196, 0.30);
  box-shadow: 14px 14px 14px ;
  border-radius: 16px;
  filter: blur(7px);
`;

const StyledTodospan = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1101 = styled.div`
  width: 8px;
  height: 8px;
  left: 0px;
  top: 5px;
  position: absolute;
  background: #5030E5;
  border-radius: 9999px;
`;

const StyledRectangle1058 = styled.div`
  width: 355px;
  height: 625px;
  background: #F5F5F5;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
`;

const StyledRectangle1059 = styled.div`
  width: 354px;
  height: 625px;
  background: #F5F5F5;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
`;

const StyledRectangle1060 = styled.div`
  width: 417px;
  height: 44px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: #F5F5F5;
  border-radius: 6px;
`;

const StyledSearchforanythingspan = styled.span`
  color: #787486;
  font-size: 14px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledVector58 = styled.div`
  width: 17.42px;
  height: 17.42px;
  left: 1.83px;
  top: 1.83px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector59 = styled.div`
  width: 1.83px;
  height: 1.83px;
  left: 18.33px;
  top: 18.33px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector60 = styled.div`
  width: 22px;
  height: 22px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector61 = styled.div`
  width: 13px;
  height: 6.45px;
  left: 2.50px;
  top: 6.15px;
  position: absolute;
  background: #292D32;
`;

const StyledVector62 = styled.div`
  width: 18px;
  height: 18px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledAnimaagrawalspan = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledUpindiaspan = styled.span`
  color: #787486;
  font-size: 14px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledVector63 = styled.div`
  width: 16.04px;
  height: 16.87px;
  left: 3.97px;
  top: 2.91px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector64 = styled.div`
  width: 3.70px;
  height: 1.26px;
  left: 10.17px;
  top: 1.94px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector65 = styled.div`
  width: 6px;
  height: 3px;
  left: 9.02px;
  top: 19.06px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector66 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledEllipse18 = styled.div`
  width: 6px;
  height: 6px;
  left: 15px;
  top: 1px;
  position: absolute;
  background: #D8727D;
  border-radius: 9999px;
`;

const StyledVector67 = styled.div`
  width: 20px;
  height: 19.13px;
  left: 2px;
  top: 2.43px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector68 = styled.div`
  width: 3.32px;
  height: 4.50px;
  left: 10.34px;
  top: 6.86px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector69 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector70 = styled.div`
  width: 1px;
  height: 1px;
  left: 11.50px;
  top: 13.25px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector74 = styled.div`
  width: 18px;
  height: 18.50px;
  left: 3px;
  top: 3.50px;
  position: absolute;
  outline: 1.50px #787486 solid;
  outline-offset: -0.75px;
`;

const StyledVector75 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector76 = styled.div`
  width: 1px;
  height: 1px;
  left: 11.50px;
  top: 13.20px;
  position: absolute;
  outline: 2px #787486 solid;
  outline-offset: -1px;
`;

const StyledVector77 = styled.div`
  width: 1px;
  height: 1px;
  left: 7.80px;
  top: 13.20px;
  position: absolute;
  outline: 2px #787486 solid;
  outline-offset: -1px;
`;

const StyledVector78 = styled.div`
  width: 1px;
  height: 1px;
  left: 7.80px;
  top: 16.20px;
  position: absolute;
  outline: 2px #787486 solid;
  outline-offset: -1px;
`;

const StyledVector81 = styled.div`
  width: 12px;
  height: 12.34px;
  left: 151px;
  top: 14.33px;
  position: absolute;
  outline: 1.30px #787486 solid;
  outline-offset: -0.65px;
`;

const StyledVector83 = styled.div`
  width: 3.33px;
  height: 4.66px;
  left: 155.33px;
  top: 17.50px;
  position: absolute;
  background: #787486;
`;

const StyledVector84 = styled.div`
  width: 16px;
  height: 16px;
  left: 149px;
  top: 12px;
  position: absolute;
  opacity: 0;
`;

const StyledRectangle106201 = styled.div`
  width: 122px;
  height: 40px;
  left: 134px;
  top: 0px;
  position: absolute;
  border-radius: 6px;
  border: 1px #787486 solid;
`;

const StyledVector85 = styled.div`
  width: 11.56px;
  height: 5.73px;
  left: 2.22px;
  top: 5.47px;
  position: absolute;
  background: #787486;
`;

const StyledVector86 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledTodayspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  text-transform: capitalize;
  word-wrap: break-word;
`;

const StyledVector87 = styled.div`
  width: 11.47px;
  height: 13.20px;
  left: 2.27px;
  top: 1px;
  position: absolute;
  outline: 1.30px #787486 solid;
  outline-offset: -0.65px;
`;

const StyledVector88 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector89 = styled.div`
  width: 11.56px;
  height: 5.73px;
  left: 2.22px;
  top: 5.47px;
  position: absolute;
  background: #787486;
`;

const StyledVector90 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledRectangle1063 = styled.div`
  width: 122px;
  height: 40px;
  left: 0px;
  top: 0px;
  position: absolute;
  border-radius: 6px;
  border: 1px #787486 solid;
`;

const StyledFilterspan = styled.span`
  color: #787486;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  text-transform: capitalize;
  word-wrap: break-word;
`;

const StyledVector91 = styled.div`
  width: 25px;
  height: 25px;
  left: 2.50px;
  top: 2.50px;
  position: absolute;
  background: rgba(80, 48, 229, 0.20);
`;

const StyledVector92 = styled.div`
  width: 12.51px;
  height: 12.51px;
  left: 8.75px;
  top: 8.75px;
  position: absolute;
  outline: 1.50px #5030E5 solid;
  outline-offset: -0.75px;
`;

const StyledVector93 = styled.div`
  width: 4.06px;
  height: 4.06px;
  left: 15.21px;
  top: 10.73px;
  position: absolute;
  outline: 1.50px #5030E5 solid;
  outline-offset: -0.75px;
`;

const StyledVector94 = styled.div`
  width: 30px;
  height: 30px;
  left: 30px;
  top: 30px;
  position: absolute;
  transform: rotate(-180deg);
  transform-origin: top left;
  opacity: 0;
`;

const StyledVector95 = styled.div`
  width: 25px;
  height: 25px;
  left: 2.50px;
  top: 2.50px;
  position: absolute;
  background: rgba(80, 48, 229, 0.20);
`;

const StyledVector96 = styled.div`
  width: 30px;
  height: 30px;
  left: 30px;
  top: 30px;
  position: absolute;
  transform: rotate(-180deg);
  transform-origin: top left;
  opacity: 0;
`;

const StyledVector97 = styled.div`
  width: 5.67px;
  height: 8.33px;
  left: 9.49px;
  top: 3.83px;
  position: absolute;
  background: #5030E5;
  outline: 0.30px #5030E5 solid;
`;

const StyledVector98 = styled.div`
  width: 5.67px;
  height: 8.33px;
  left: 0.83px;
  top: 3.83px;
  position: absolute;
  background: #5030E5;
  outline: 0.30px #5030E5 solid;
`;

const StyledVector99 = styled.div`
  width: 6.33px;
  height: 1px;
  left: 4.83px;
  top: 7.50px;
  position: absolute;
  background: #5030E5;
  outline: 0.30px #5030E5 solid;
`;

const StyledVector100 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledMobileapp01span = styled.span`
  color: #0D062D;
  font-size: 46px;
  font-family: Inter;
  font-weight: 600;
  text-transform: capitalize;
  word-wrap: break-word;
`;

const StyledRectangle1065 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: #E0E0E0;
  border-radius: 10px;
`;

const Styled4span = styled.span`
  color: #625F6D;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle106501 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: #E0E0E0;
  border-radius: 10px;
`;

const Styled3span = styled.span`
  color: #625F6D;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle106502 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: #E0E0E0;
  border-radius: 10px;
`;

const Styled2span = styled.span`
  color: #625F6D;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse20 = styled.div`
  width: 33px;
  height: 33px;
  background: rgba(252, 214, 74, 0.70);
  box-shadow: 34px 34px 34px ;
  border-radius: 9999px;
  filter: blur(17px);
`;

const StyledOnprogressspan = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1102 = styled.div`
  width: 8px;
  height: 8px;
  left: 0px;
  top: 5px;
  position: absolute;
  background: #FFA500;
  border-radius: 9999px;
`;

const StyledDonespan = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1103 = styled.div`
  width: 8px;
  height: 8px;
  left: 0px;
  top: 5px;
  position: absolute;
  background: #76A5EA;
  border-radius: 9999px;
`;

const StyledRectangle106701 = styled.div`
  width: 314px;
  height: 177px;
  background: rgba(80, 48, 229, 0.06);
  border-radius: 10px;
  border: 1px rgba(80, 48, 229, 0.59) solid;
`;

const StyledRectangle106602 = styled.div`
  width: 314px;
  height: 177px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledVector101 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector102 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 5.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector103 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector105 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 0.83px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector106 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.33px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector107 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector108 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 4.67px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector109 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const Styled12comments01span = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled15filesspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1501 = styled.div`
  width: 24px;
  height: 24px;
  left: 59px;
  top: 133px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1301 = styled.div`
  width: 25px;
  height: 24px;
  left: 39px;
  top: 133px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1201 = styled.div`
  width: 24px;
  height: 24px;
  left: 20px;
  top: 133px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledDesignsystem01span = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled02span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledItjustneedstoadapttheuifromwhatyoudidbeforespan = styled.span`
  color: #0D062D;
  font-size: 12px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledRectangle106702 = styled.div`
  width: 76px;
  height: 23px;
  left: 20px;
  top: 20px;
  position: absolute;
  background: rgba(131, 194, 157, 0.20);
  border-radius: 4px;
`;

const StyledCompletedspan = styled.span`
  color: #68B266;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle106603 = styled.div`
  width: 314px;
  height: 227px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledUnsplashBS1XGRkIH4 = styled.div`
  width: 131px;
  height: 79px;
  left: 20px;
  top: 76px;
  position: absolute;
  background: #C4C4C4;
  border-radius: 8px;
`;

const StyledUnsplashKIqJfzbII9w = styled.div`
  width: 131px;
  height: 79px;
  left: 163px;
  top: 76px;
  position: absolute;
  background: #C4C4C4;
  border-radius: 8px;
`;

const StyledVector110 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector111 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 5.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector112 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector114 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 0.83px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector115 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.33px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector116 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector117 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 4.67px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector118 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const Styled9commentsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled10filesspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1202 = styled.div`
  width: 24px;
  height: 24px;
  left: 20px;
  top: 183px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledMoodboardspan = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled03span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledRectangle106703 = styled.div`
  width: 36px;
  height: 23px;
  left: 20px;
  top: 20px;
  position: absolute;
  background: rgba(223, 168, 116, 0.20);
  border-radius: 4px;
`;

const StyledLow01span = styled.span`
  color: #D58D49;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle106604 = styled.div`
  width: 314px;
  height: 258px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledUnsplashMicqqGyDQ6w = styled.div`
  width: 281.68px;
  height: 110px;
  left: 20px;
  top: 76px;
  position: absolute;
  background: #C4C4C4;
  border-radius: 8px;
`;

const StyledVector119 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector120 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 5.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector121 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector123 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 0.83px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector124 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.33px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector125 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector126 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 4.67px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector127 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const Styled14commentsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled15files01span = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1502 = styled.div`
  width: 24px;
  height: 24px;
  left: 59px;
  top: 214px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1302 = styled.div`
  width: 25px;
  height: 24px;
  left: 39px;
  top: 214px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1203 = styled.div`
  width: 24px;
  height: 24px;
  left: 20px;
  top: 214px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledOnboardingillustrationsspan = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled04span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledRectangle106704 = styled.div`
  width: 36px;
  height: 23px;
  left: 20px;
  top: 20px;
  position: absolute;
  background: rgba(223, 168, 116, 0.20);
  border-radius: 4px;
`;

const StyledLow02span = styled.span`
  color: #D58D49;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle106605 = styled.div`
  width: 314px;
  height: 328px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledRectangle1068 = styled.div`
  width: 274px;
  height: 180px;
  left: 20px;
  top: 76px;
  position: absolute;
  background: #C4C4C4;
  border-radius: 8px;
`;

const StyledRectangle106801 = styled.div`
  width: 274px;
  height: 180px;
  left: 20px;
  top: 76px;
  position: absolute;
  background: #C4C4C4;
  border-radius: 8px;
`;

const StyledPlantCareAppDribbbleshot1 = styled.div`
  width: 304px;
  height: 228px;
  left: 5px;
  top: 76px;
  position: absolute;
`;

const StyledVector128 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector129 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 5.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector130 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector132 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 0.83px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector133 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.33px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector134 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector135 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 4.67px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector136 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const Styled12comments02span = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled15files02span = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1303 = styled.div`
  width: 25px;
  height: 24px;
  left: 39px;
  top: 284px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1204 = styled.div`
  width: 24px;
  height: 24px;
  left: 20px;
  top: 284px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledMobileappdesignspan = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled05span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledRectangle106705 = styled.div`
  width: 76px;
  height: 23px;
  left: 20px;
  top: 20px;
  position: absolute;
  background: rgba(131, 194, 157, 0.20);
  border-radius: 4px;
`;

const StyledCompleted01span = styled.span`
  color: #68B266;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledInvitespan = styled.span`
  color: #5030E5;
  font-size: 16px;
  font-family: Inter;
  font-weight: 500;
  text-transform: capitalize;
  word-wrap: break-word;
`;

const StyledVector139 = styled.div`
  width: 15px;
  height: 15px;
  left: 1.50px;
  top: 1.50px;
  position: absolute;
  background: rgba(80, 48, 229, 0.20);
`;

const StyledVector140 = styled.div`
  width: 18px;
  height: 18px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledEllipse1205 = styled.div`
  width: 38px;
  height: 38px;
  left: 79px;
  top: 0px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1304 = styled.div`
  width: 38px;
  height: 38px;
  left: 109px;
  top: 0px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1503 = styled.div`
  width: 38px;
  height: 38px;
  left: 139px;
  top: 0px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse14 = styled.div`
  width: 38px;
  height: 38px;
  left: 169px;
  top: 0px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse17 = styled.div`
  width: 38px;
  height: 38px;
  left: 199px;
  top: 0px;
  position: absolute;
  background: #F4D7DA;
  border-radius: 9999px;
  border: 1px white solid;
`;

const Styled2span = styled.span`
  color: #D25B68;
  font-size: 15px;
  font-family: Inter;
  font-weight: 500;
  text-transform: capitalize;
  word-wrap: break-word;
`;

const StyledRectangle1073 = styled.div`
  width: 354px;
  height: 147px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: #C4C4C4;
`;

const StyledRectangle106606 = styled.div`
  width: 314px;
  height: 177px;
  left: 20px;
  top: 7px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledVector141 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector142 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 5.33px;
  top: 1.33px;
  position: absolute;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector143 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledVector145 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 0.83px;
  top: 0.83px;
  position: absolute;
  background: #787486;
`;

const StyledVector146 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.33px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector147 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector148 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 4.67px;
  top: 6.67px;
  position: absolute;
  background: #787486;
`;

const StyledVector149 = styled.div`
  width: 16px;
  height: 16px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const Styled2commentsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled0files01span = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1504 = styled.div`
  width: 24px;
  height: 24px;
  left: 79px;
  top: 140px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1305 = styled.div`
  width: 25px;
  height: 24px;
  left: 59px;
  top: 140px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1206 = styled.div`
  width: 24px;
  height: 24px;
  left: 40px;
  top: 140px;
  position: absolute;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledWireframes01span = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled06span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledLowfidelitywireframesincludethemostbasiccontentandvisualsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledRectangle106706 = styled.div`
  width: 39px;
  height: 23px;
  left: 40px;
  top: 27px;
  position: absolute;
  background: rgba(216, 114, 125, 0.10);
  border-radius: 4px;
`;

const StyledHighspan = styled.span`
  color: #D8727D;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledRectangle1074 = styled.div`
  width: 339px;
  height: 23px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: #C4C4C4;
`;

const StyledRectangle106607 = styled.div`
  width: 314px;
  height: 227px;
  left: 14px;
  top: 14px;
  position: absolute;
  background: white;
  border-radius: 16px;
`;

const StyledRectangle1076 = styled.div`
  width: 166px;
  height: 40px;
  background: white;
  border-radius: 4px;
`;

const StyledWriteamessagespan = styled.span`
  color: black;
  font-size: 14px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledVector150 = styled.div`
  width: 16.07px;
  height: 17.21px;
  left: 3.97px;
  top: 2px;
  position: absolute;
  background: #FBCB18;
`;

const StyledVector151 = styled.div`
  width: 7.92px;
  height: 1.85px;
  left: 8.05px;
  top: 20.15px;
  position: absolute;
  background: #FBCB18;
`;

const StyledVector152 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledThoughtstimespan = styled.span`
  color: black;
  font-size: 14px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledWedonthaveanynoticeforyoutillthenyoucanshareyourthoughtswithyourpeersspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledVector153 = styled.div`
  width: 19.99px;
  height: 20px;
  left: 2px;
  top: 2px;
  position: absolute;
  opacity: 0.20;
  background: #5030E5;
`;

const StyledVector154 = styled.div`
  width: 9.50px;
  height: 9.50px;
  left: 7.25px;
  top: 7.25px;
  position: absolute;
  background: #5030E5;
`;

const StyledVector155 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
  opacity: 0;
`;

const StyledRectangle106608 = styled.div`
  width: 314px;
  height: 177px;
  left: 9.26px;
  top: 0px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  background: white;
  border-radius: 16px;
`;

const StyledVector156 = styled.div`
  width: 13.33px;
  height: 13.33px;
  left: 1.77px;
  top: 1.38px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector157 = styled.div`
  width: 8px;
  height: 2.92px;
  left: 6.09px;
  top: 1.61px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  outline: 1px #787486 solid;
  outline-offset: -0.50px;
`;

const StyledVector158 = styled.div`
  width: 16px;
  height: 16px;
  left: 0.84px;
  top: 0px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  opacity: 0;
`;

const StyledVector160 = styled.div`
  width: 14.33px;
  height: 14.37px;
  left: 1.63px;
  top: 0.88px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  background: #787486;
`;

const StyledVector161 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 7.81px;
  top: 7.04px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  background: #787486;
`;

const StyledVector162 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 10.47px;
  top: 7.18px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  background: #787486;
`;

const StyledVector163 = styled.div`
  width: 1.33px;
  height: 1.33px;
  left: 5.15px;
  top: 6.90px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  background: #787486;
`;

const StyledVector164 = styled.div`
  width: 16px;
  height: 16px;
  left: 0.84px;
  top: 0px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  opacity: 0;
`;

const Styled10commentsspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const Styled3filesspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledEllipse1306 = styled.div`
  width: 25px;
  height: 24px;
  left: 41.25px;
  top: 134.86px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledEllipse1207 = styled.div`
  width: 24px;
  height: 24px;
  left: 22.28px;
  top: 133.86px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  border-radius: 9999px;
  border: 1px white solid;
`;

const StyledResearchspan = styled.span`
  color: #0D062D;
  font-size: 18px;
  font-family: Inter;
  font-weight: 600;
  word-wrap: break-word;
`;

const Styled07span = styled.span`
  color: #0D062D;
  font-size: 16px;
  font-family: Inter;
  font-weight: 800;
  word-wrap: break-word;
`;

const StyledUserresearchhelpsyoutocreateanoptimalproductforusersspan = styled.span`
  color: #787486;
  font-size: 12px;
  font-family: Inter;
  font-weight: 400;
  word-wrap: break-word;
`;

const StyledRectangle106707 = styled.div`
  width: 39px;
  height: 23px;
  left: 28.19px;
  top: 21.02px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
  background: rgba(216, 114, 125, 0.10);
  border-radius: 4px;
`;

const StyledHigh01span = styled.span`
  color: #D8727D;
  font-size: 12px;
  font-family: Inter;
  font-weight: 500;
  word-wrap: break-word;
`;

const StyledShape = styled.div`
  width: 12.82px;
  height: 11.58px;
  left: 5.43px;
  top: 6.37px;
  position: absolute;
  background: white;
`;

const StyledShape01 = styled.div`
  width: 12.82px;
  height: 11.58px;
  left: 5.43px;
  top: 6.37px;
  position: absolute;
  outline: 0.75px black solid;
  outline-offset: -0.38px;
`;

const StyledShape02 = styled.div`
  width: 0.75px;
  height: 4.20px;
  left: 15px;
  top: 11px;
  position: absolute;
  background: black;
`;

const StyledShape03 = styled.div`
  width: 0.77px;
  height: 4.20px;
  left: 13px;
  top: 11px;
  position: absolute;
  background: black;
`;

const StyledShape04 = styled.div`
  width: 0.77px;
  height: 4.20px;
  left: 11px;
  top: 11px;
  position: absolute;
  background: black;
`;

const StyledRectangle1061 = styled.div`
  width: 38px;
  height: 38px;
  left: 0px;
  top: 0px;
  position: absolute;
  background: black;
  border-radius: 19px;
`;

const StyledImage1 = styled.img`
  width: 47px;
  height: 71px;
  left: -4px;
  top: -5px;
  position: absolute;
`;

const StyledGroup8 = styled.div`
  width: 98px;
  height: 24px;
  position: relative;
`;

const StyledVuesaxoutlineprofile2user = styled.div`
  width: 16px;
  height: 16px;
  left: 15px;
  top: 13px;
  position: absolute;
`;

const StyledVuesaxlinearpause = styled.div`
  width: 20px;
  height: 20px;
  left: 147px;
  top: 30px;
  position: absolute;
  transform: rotate(-90deg);
  transform-origin: top left;
`;

const StyledVuesaxlinearmenu = styled.div`
  width: 21px;
  height: 21px;
  left: 199px;
  top: 10px;
  position: absolute;
`;

const StyledVuesaxoutlinecategory = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlineartasksquare = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearmessage = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearprofile2user = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearsetting2 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearaddsquare = styled.div`
  width: 16px;
  height: 16px;
  left: 187px;
  top: 0px;
  position: absolute;
`;

const StyledGroup55 = styled.div`
  width: 225px;
  height: 39px;
  position: relative;
`;

const StyledGroup56 = styled.div`
  width: 162px;
  height: 19px;
  position: relative;
`;

const StyledGroup57 = styled.div`
  width: 139px;
  height: 19px;
  position: relative;
`;

const StyledGroup58 = styled.div`
  width: 113px;
  height: 19px;
  position: relative;
`;

const StyledVuesaxbulkcolorfilter = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxoutlinearrowleft = styled.div`
  width: 20px;
  height: 20px;
  left: 6px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxoutlinearrowleft01 = styled.div`
  width: 20px;
  height: 20px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearfolder2 = styled.div`
  width: 16px;
  height: 16px;
  left: 238px;
  top: 137px;
  position: absolute;
`;

const StyledVuesaxoutlinemessage = styled.div`
  width: 16px;
  height: 16px;
  left: 126px;
  top: 137px;
  position: absolute;
`;

const StyledGroup620 = styled.div`
  width: 61px;
  height: 19px;
  position: relative;
`;

const StyledVuesaxtwotonesearchnormal = styled.div`
  width: 22px;
  height: 22px;
  left: 16.80px;
  top: 10px;
  position: absolute;
`;

const StyledVuesaxoutlinearrowdown = styled.div`
  width: 18px;
  height: 18px;
  left: 183px;
  top: 10px;
  position: absolute;
`;

const StyledVuesaxlinearnotification = styled.div`
  width: 24px;
  height: 24px;
  left: 96px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearmessagequestion = styled.div`
  width: 24px;
  height: 24px;
  left: 48px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxlinearcalendar2 = styled.div`
  width: 24px;
  height: 24px;
  left: 0px;
  top: 0px;
  position: absolute;
`;

const StyledVuesaxoutlinearrowdown01 = styled.div`
  width: 16px;
  height: 16px;
  left: 228px;
  top: 13px;
  position: absolute;
`;

const StyledVuesaxlinearfilter = styled.div`
  width: 16px;
  height: 16px;
  left: 15px;
  top: 12px;
  position: absolute;
`;

const StyledVuesaxoutlinearrowdown02 = styled.div`
  width: 16px;
  height: 16px;
  left: 86px;
  top: 12px;
  position: absolute;
`;

const StyledVuesaxlineararrowsquareup = styled.div`
  width: 30px;
  height: 30px;
  left: 272px;
  top: 16px;
  position: absolute;
`;

const StyledVuesaxlineararrowsquareup01 = styled.div`
  width: 30px;
  height: 30px;
  left: 314px;
  top: 16px;
  position: absolute;
`;

const StyledVuesaxoutlinelink = styled.div`
  width: 16px;
  height: 16px;
  left: 321px;
  top: 23px;
  position: absolute;
`;

const StyledGroup621 = styled.div`
  width: 20px;
  height: 20px;
  position: relative;
  border-radius: 10px;
`;

const StyledGroup649 = styled.div`
  width: 20px;
  height: 20px;
  position: relative;
  border-radius: 10px;
`;

const StyledGroup650 = styled.div`
  width: 20px;
  height: 20px;
  position: relative;
  border-radius: 10px;
`;

const StyledGroup622 = styled.div`
  width: 111px;
  height: 19px;
  position: relative;
`;

const StyledGroup624 = styled.div`
  width: 57px;
  height: 19px;
  position: relative;
`;

const StyledVuesaxlinearfolder201 = styled.div`
  width: 16px;
  height: 16px;
  left: 232px;
  top: 137px;
  position: absolute;
`;

const StyledVuesaxoutlinemessage01 = styled.div`
  width: 16px;
  height: 16px;
  left: 120px;
  top: 137px;
  position: absolute;
`;

const StyledVuesaxlinearfolder202 = styled.div`
  width: 16px;
  height: 16px;
  left: 232px;
  top: 187px;
  position: absolute;
`;

const StyledVuesaxoutlinemessage02 = styled.div`
  width: 16px;
  height: 16px;
  left: 125px;
  top: 187px;
  position: absolute;
`;

const StyledVuesaxlinearfolder203 = styled.div`
  width: 16px;
  height: 16px;
  left: 232px;
  top: 218px;
  position: absolute;
`;

const StyledVuesaxoutlinemessage03 = styled.div`
  width: 16px;
  height: 16px;
  left: 120px;
  top: 218px;
  position: absolute;
`;

const StyledVuesaxlinearfolder204 = styled.div`
  width: 16px;
  height: 16px;
  left: 232px;
  top: 288px;
  position: absolute;
`;

const StyledVuesaxoutlinemessage04 = styled.div`
  width: 16px;
  height: 16px;
  left: 120px;
  top: 288px;
  position: absolute;
`;

const StyledVuesaxlinearaddsquare01 = styled.div`
  width: 18px;
  height: 18px;
  left: 0px;
  top: 11px;
  position: absolute;
`;

const StyledVuesaxlinearfolder205 = styled.div`
  width: 16px;
  height: 16px;
  left: 258px;
  top: 144px;
  position: absolute;
`;

const StyledVuesaxoutlinemessage05 = styled.div`
  width: 16px;
  height: 16px;
  left: 152px;
  top: 144px;
  position: absolute;
`;

const StyledMaskGroup01 = styled.div`
  width: 339px;
  height: 23px;
  position: relative;
`;

const StyledVuesaxbulklampon = styled.div`
  width: 24px;
  height: 24px;
  position: relative;
`;

const StyledVuesaxbulkaddsquare = styled.div`
  width: 24px;
  height: 24px;
  position: relative;
`;

const StyledVuesaxlinearfolder206 = styled.div`
  width: 16px;
  height: 16px;
  left: 239.77px;
  top: 149.27px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
`;

const StyledVuesaxoutlinemessage06 = styled.div`
  width: 16px;
  height: 16px;
  left: 127.92px;
  top: 143.41px;
  position: absolute;
  transform: rotate(3deg);
  transform-origin: top left;
`;

const StyledCursorGrabbed = styled.div`
  width: 24px;
  height: 24px;
  position: relative;
  overflow: hidden;
`;

const StyledMaskGroup02 = styled.div`
  width: 38px;
  height: 38px;
  position: relative;
`;

const StyledGroup617 = styled.div`
  width: 220px;
  height: 40px;
  position: relative;
`;

const StyledGroup53 = styled.div`
  width: 84px;
  height: 24px;
  position: relative;
`;

const StyledGroup50 = styled.div`
  width: 83px;
  height: 24px;
  position: relative;
`;

const StyledGroup49 = styled.div`
  width: 116px;
  height: 24px;
  position: relative;
`;

const StyledGroup51 = styled.div`
  width: 111px;
  height: 24px;
  position: relative;
`;

const StyledGroup52 = styled.div`
  width: 103px;
  height: 24px;
  position: relative;
`;

const StyledGroup54 = styled.div`
  width: 203px;
  height: 16px;
  position: relative;
`;

const StyledGroup7 = styled.div`
  width: 24px;
  height: 24px;
  position: relative;
`;

const StyledGroup639 = styled.div`
  width: 26px;
  height: 20px;
  position: relative;
`;

const StyledGroup633 = styled.div`
  width: 314px;
  height: 177px;
  position: relative;
`;

const StyledGroup598 = styled.div`
  width: 417px;
  height: 44px;
  position: relative;
`;

const StyledGroup603 = styled.div`
  width: 201px;
  height: 39px;
  position: relative;
`;

const StyledGroup640 = styled.div`
  width: 120px;
  height: 24px;
  position: relative;
`;

const StyledGroup647 = styled.div`
  width: 256px;
  height: 40px;
  position: relative;
`;

const StyledGroup651 = styled.div`
  width: 344px;
  height: 56px;
  position: relative;
`;

const StyledGroup637 = styled.div`
  width: 314px;
  height: 177px;
  position: relative;
`;

const StyledGroup632 = styled.div`
  width: 314px;
  height: 227px;
  position: relative;
`;

const StyledGroup635 = styled.div`
  width: 314px;
  height: 258px;
  position: relative;
`;

const StyledGroup636 = styled.div`
  width: 314px;
  height: 328px;
  position: relative;
`;

const StyledGroup646 = styled.div`
  width: 237px;
  height: 38px;
  position: relative;
`;

const StyledMaskGroup = styled.div`
  width: 354px;
  height: 147px;
  position: relative;
`;

const StyledGroup652 = styled.div`
  width: 322.83px;
  height: 193.19px;
  position: relative;
`;

export const Rectangle1 = () => {
  return (
    <StyledRectangle1 />
    <StyledGroup617>
      <StyledVuesaxoutlineprofile2user>
        <StyledVector />
        <StyledVector01 />
        <StyledVector02 />
        <StyledVector03 />
        <StyledVector04 />
      </StyledVuesaxoutlineprofile2user>
      <StyledRectangle1062 />
      <StyledShare>Share</StyledShare>
      <StyledRectangle1064 />
      <StyledVuesaxlinearpause>
        <StyledVector05 />
        <StyledVector06 />
        <StyledVector07 />
      </StyledVuesaxlinearpause>
      <StyledVuesaxlinearmenu>
        <StyledVector08 />
        <StyledVector09 />
        <StyledVector10 />
        <StyledVector11 />
        <StyledVector12 />
      </StyledVuesaxlinearmenu>
    </StyledGroup617>
    <StyledGroup53>
      <StyledVuesaxoutlinecategory>
        <StyledVector13 />
        <StyledVector14 />
        <StyledVector15 />
        <StyledVector16 />
        <StyledVector17 />
      </StyledVuesaxoutlinecategory>
      <StyledHome>Home</StyledHome>
    </StyledGroup53>
    <StyledGroup50>
      <StyledVuesaxlineartasksquare>
        <StyledVector19 />
        <StyledVector21 />
        <StyledVector22 />
        <StyledVector23 />
      </StyledVuesaxlineartasksquare>
      <StyledTasks>Tasks</StyledTasks>
    </StyledGroup50>
    <StyledGroup49>
      <StyledVuesaxlinearmessage>
        <StyledVector24 />
        <StyledVector25 />
        <StyledVector26 />
        <StyledVector27 />
        <StyledVector28 />
      </StyledVuesaxlinearmessage>
      <StyledMessages>Messages</StyledMessages>
    </StyledGroup49>
    <StyledGroup51>
      <StyledVuesaxlinearprofile2user>
        <StyledVector29 />
        <StyledVector30 />
        <StyledVector31 />
        <StyledVector32 />
        <StyledVector33 />
      </StyledVuesaxlinearprofile2user>
      <StyledMembers>Members</StyledMembers>
    </StyledGroup51>
    <StyledGroup52>
      <StyledVuesaxlinearsetting2>
        <StyledVector34 />
        <StyledVector35 />
        <StyledVector36 />
      </StyledVuesaxlinearsetting2>
      <StyledSettings>Settings</StyledSettings>
    </StyledGroup52>
    <StyledGroup54>
      <StyledVuesaxlinearaddsquare>
        <StyledVector39 />
        <StyledVector40 />
      </StyledVuesaxlinearaddsquare>
      <StyledMyprojects>my projects</StyledMyprojects>
    </StyledGroup54>
    <StyledGroup55>
      <StyledRectangle1056 />
      <StyledMobileApp>Mobile App</StyledMobileApp>
      <StyledP>. . .</StyledP>
      <StyledEllipse8 />
    </StyledGroup55>
    <StyledGroup56>
      <StyledWebsiteRedesign>Website Redesign</StyledWebsiteRedesign>
      <StyledEllipse9 />
    </StyledGroup56>
    <StyledGroup57>
      <StyledDesignSystem>Design System</StyledDesignSystem>
      <StyledEllipse10 />
    </StyledGroup57>
    <StyledGroup58>
      <StyledWireframes>Wireframes</StyledWireframes>
      <StyledEllipse11 />
    </StyledGroup58>
    <StyledGroup7>
      <StyledVuesaxbulkcolorfilter>
        <StyledVector41 />
        <StyledVector42 />
        <StyledVector43 />
        <StyledVector44 />
      </StyledVuesaxbulkcolorfilter>
    </StyledGroup7>
    <StyledGroup639>
      <StyledVuesaxoutlinearrowleft>
        <StyledVector45 />
        <StyledVector46 />
      </StyledVuesaxoutlinearrowleft>
      <StyledVuesaxoutlinearrowleft01>
        <StyledVector47 />
        <StyledVector48 />
      </StyledVuesaxoutlinearrowleft01>
    </StyledGroup639>
    <StyledGroup8>
      <StyledProjectM>Project M.</StyledProjectM>
    </StyledGroup8>
    <StyledRectangle1057 />
    <StyledGroup633>
      <StyledRectangle1066 />
      <StyledVuesaxlinearfolder2>
        <StyledVector49 />
        <StyledVector50 />
        <StyledVector51 />
      </StyledVuesaxlinearfolder2>
      <StyledVuesaxoutlinemessage>
        <StyledVector53 />
        <StyledVector54 />
        <StyledVector55 />
        <StyledVector56 />
        <StyledVector57 />
      </StyledVuesaxoutlinemessage>
      <Styled12comments>12 comments</Styled12comments>
      <Styled0files>0 files</Styled0files>
      <StyledEllipse15  src="https://placehold.co/24x24"/>
      <StyledEllipse13  src="https://placehold.co/25x24"/>
      <StyledEllipse12  src="https://placehold.co/24x24"/>
      <StyledBrainstorming>Brainstorming</StyledBrainstorming>
      <Styled01>. . .</Styled01>
      <StyledBrainstormingbringsteammembersdiverseexperienceintoplay>Brainstorming brings team members' diverse experience into play. </StyledBrainstormingbringsteammembersdiverseexperienceintoplay>
      <StyledRectangle1067 />
      <StyledLow>Low</StyledLow>
    </StyledGroup633>
    <StyledRectangle106601 />
    <StyledGroup620>
      <StyledToDo>To Do</StyledToDo>
      <StyledEllipse1101 />
    </StyledGroup620>
    <StyledRectangle1058 />
    <StyledRectangle1059 />
    <StyledGroup598>
      <StyledRectangle1060 />
      <StyledSearchforanything>Search for anything...</StyledSearchforanything>
      <StyledVuesaxtwotonesearchnormal>
        <StyledVector58 />
        <StyledVector59 />
        <StyledVector60 />
      </StyledVuesaxtwotonesearchnormal>
    </StyledGroup598>
    <StyledGroup603>
      <StyledVuesaxoutlinearrowdown>
        <StyledVector61 />
        <StyledVector62 />
      </StyledVuesaxoutlinearrowdown>
      <StyledAnimaAgrawal>Anima Agrawal</StyledAnimaAgrawal>
      <StyledUPIndia>U.P, India</StyledUPIndia>
    </StyledGroup603>
    <StyledGroup640>
      <StyledVuesaxlinearnotification>
        <StyledVector63 />
        <StyledVector64 />
        <StyledVector65 />
        <StyledVector66 />
        <StyledEllipse18 />
      </StyledVuesaxlinearnotification>
      <StyledVuesaxlinearmessagequestion>
        <StyledVector67 />
        <StyledVector68 />
        <StyledVector69 />
        <StyledVector70 />
      </StyledVuesaxlinearmessagequestion>
      <StyledVuesaxlinearcalendar2>
        <StyledVector74 />
        <StyledVector75 />
        <StyledVector76 />
        <StyledVector77 />
        <StyledVector78 />
      </StyledVuesaxlinearcalendar2>
    </StyledGroup640>
    <StyledGroup647>
      <StyledVector81 />
      <StyledVector83 />
      <StyledVector84 />
      <StyledRectangle106201 />
      <StyledVuesaxoutlinearrowdown01>
        <StyledVector85 />
        <StyledVector86 />
      </StyledVuesaxoutlinearrowdown01>
      <StyledToday>Today</StyledToday>
      <StyledVuesaxlinearfilter>
        <StyledVector87 />
        <StyledVector88 />
      </StyledVuesaxlinearfilter>
      <StyledVuesaxoutlinearrowdown02>
        <StyledVector89 />
        <StyledVector90 />
      </StyledVuesaxoutlinearrowdown02>
      <StyledRectangle1063 />
      <StyledFilter>Filter</StyledFilter>
    </StyledGroup647>
    <StyledGroup651>
      <StyledVuesaxlineararrowsquareup>
        <StyledVector91 />
        <StyledVector92 />
        <StyledVector93 />
        <StyledVector94 />
      </StyledVuesaxlineararrowsquareup>
      <StyledVuesaxlineararrowsquareup01>
        <StyledVector95 />
        <StyledVector96 />
      </StyledVuesaxlineararrowsquareup01>
      <StyledVuesaxoutlinelink>
        <StyledVector97 />
        <StyledVector98 />
        <StyledVector99 />
        <StyledVector100 />
      </StyledVuesaxoutlinelink>
      <StyledMobileApp01>Mobile App</StyledMobileApp01>
    </StyledGroup651>
    <StyledGroup621>
      <StyledRectangle1065 />
      <Styled4>4</Styled4>
    </StyledGroup621>
    <StyledGroup649>
      <StyledRectangle106501 />
      <Styled3>3</Styled3>
    </StyledGroup649>
    <StyledGroup650>
      <StyledRectangle106502 />
      <Styled2>2</Styled2>
    </StyledGroup650>
    <StyledEllipse20 />
    <StyledGroup622>
      <StyledOnProgress>On Progress</StyledOnProgress>
      <StyledEllipse1102 />
    </StyledGroup622>
    <StyledGroup624>
      <StyledDone>Done</StyledDone>
      <StyledEllipse1103 />
    </StyledGroup624>
    <StyledRectangle106701 />
    <StyledGroup637>
      <StyledRectangle106602 />
      <StyledVuesaxlinearfolder201>
        <StyledVector101 />
        <StyledVector102 />
        <StyledVector103 />
      </StyledVuesaxlinearfolder201>
      <StyledVuesaxoutlinemessage01>
        <StyledVector105 />
        <StyledVector106 />
        <StyledVector107 />
        <StyledVector108 />
        <StyledVector109 />
      </StyledVuesaxoutlinemessage01>
      <Styled12comments01>12 comments</Styled12comments01>
      <Styled15files>15 files</Styled15files>
      <StyledEllipse1501  src="https://placehold.co/24x24"/>
      <StyledEllipse1301  src="https://placehold.co/25x24"/>
      <StyledEllipse1201  src="https://placehold.co/24x24"/>
      <StyledDesignSystem01>Design System</StyledDesignSystem01>
      <Styled02>. . .</Styled02>
      <StyledItjustneedstoadapttheUIfromwhatyoudidbefore>It just needs to adapt the UI from what you did before </StyledItjustneedstoadapttheUIfromwhatyoudidbefore>
      <StyledRectangle106702 />
      <StyledCompleted>Completed</StyledCompleted>
    </StyledGroup637>
    <StyledGroup632>
      <StyledRectangle106603 />
      <StyledUnsplashBS1XGRkIH4  src="https://placehold.co/131x79"/>
      <StyledUnsplashKIqJfzbII9w  src="https://placehold.co/131x79"/>
      <StyledVuesaxlinearfolder202>
        <StyledVector110 />
        <StyledVector111 />
        <StyledVector112 />
      </StyledVuesaxlinearfolder202>
      <StyledVuesaxoutlinemessage02>
        <StyledVector114 />
        <StyledVector115 />
        <StyledVector116 />
        <StyledVector117 />
        <StyledVector118 />
      </StyledVuesaxoutlinemessage02>
      <Styled9comments>9 comments</Styled9comments>
      <Styled10files>10 files</Styled10files>
      <StyledEllipse1202  src="https://placehold.co/24x24"/>
      <StyledMoodboard>Moodboard</StyledMoodboard>
      <Styled03>. . .</Styled03>
      <StyledRectangle106703 />
      <StyledLow01>Low</StyledLow01>
    </StyledGroup632>
    <StyledGroup635>
      <StyledRectangle106604 />
      <StyledUnsplashMicqqGyDQ6w  src="https://placehold.co/282x110"/>
      <StyledVuesaxlinearfolder203>
        <StyledVector119 />
        <StyledVector120 />
        <StyledVector121 />
      </StyledVuesaxlinearfolder203>
      <StyledVuesaxoutlinemessage03>
        <StyledVector123 />
        <StyledVector124 />
        <StyledVector125 />
        <StyledVector126 />
        <StyledVector127 />
      </StyledVuesaxoutlinemessage03>
      <Styled14comments>14 comments</Styled14comments>
      <Styled15files01>15 files</Styled15files01>
      <StyledEllipse1502  src="https://placehold.co/24x24"/>
      <StyledEllipse1302  src="https://placehold.co/25x24"/>
      <StyledEllipse1203  src="https://placehold.co/24x24"/>
      <StyledOnboardingIllustrations>Onboarding Illustrations </StyledOnboardingIllustrations>
      <Styled04>. . .</Styled04>
      <StyledRectangle106704 />
      <StyledLow02>Low</StyledLow02>
    </StyledGroup635>
    <StyledGroup636>
      <StyledRectangle106605 />
      <StyledRectangle1068 />
      <StyledRectangle106801 />
      <StyledPlantCareAppDribbbleshot1  src="https://placehold.co/304x228"/>
      <StyledVuesaxlinearfolder204>
        <StyledVector128 />
        <StyledVector129 />
        <StyledVector130 />
      </StyledVuesaxlinearfolder204>
      <StyledVuesaxoutlinemessage04>
        <StyledVector132 />
        <StyledVector133 />
        <StyledVector134 />
        <StyledVector135 />
        <StyledVector136 />
      </StyledVuesaxoutlinemessage04>
      <Styled12comments02>12 comments</Styled12comments02>
      <Styled15files02>15 files</Styled15files02>
      <StyledEllipse1303  src="https://placehold.co/25x24"/>
      <StyledEllipse1204  src="https://placehold.co/24x24"/>
      <StyledMobileAppDesign>Mobile App Design</StyledMobileAppDesign>
      <Styled05>. . .</Styled05>
      <StyledRectangle106705 />
      <StyledCompleted01>Completed</StyledCompleted01>
    </StyledGroup636>
    <StyledGroup646>
      <StyledInvite>Invite</StyledInvite>
      <StyledVuesaxlinearaddsquare01>
        <StyledVector139 />
        <StyledVector140 />
      </StyledVuesaxlinearaddsquare01>
      <StyledEllipse1205  src="https://placehold.co/38x38"/>
      <StyledEllipse1304  src="https://placehold.co/38x38"/>
      <StyledEllipse1503  src="https://placehold.co/38x38"/>
      <StyledEllipse14  src="https://placehold.co/38x38"/>
      <StyledEllipse17 />
      <Styled2>+2</Styled2>
    </StyledGroup646>
    <StyledMaskGroup>
      <StyledRectangle1073 />
      <StyledRectangle106606 />
      <StyledVuesaxlinearfolder205>
        <StyledVector141 />
        <StyledVector142 />
        <StyledVector143 />
      </StyledVuesaxlinearfolder205>
      <StyledVuesaxoutlinemessage05>
        <StyledVector145 />
        <StyledVector146 />
        <StyledVector147 />
        <StyledVector148 />
        <StyledVector149 />
      </StyledVuesaxoutlinemessage05>
      <Styled2comments>2 comments</Styled2comments>
      <Styled0files01>0 files</Styled0files01>
      <StyledEllipse1504  src="https://placehold.co/24x24"/>
      <StyledEllipse1305  src="https://placehold.co/25x24"/>
      <StyledEllipse1206  src="https://placehold.co/24x24"/>
      <StyledWireframes01>Wireframes</StyledWireframes01>
      <Styled06>. . .</Styled06>
      <StyledLowfidelitywireframesincludethemostbasiccontentandvisuals>Low fidelity wireframes include the most basic content and visuals.</StyledLowfidelitywireframesincludethemostbasiccontentandvisuals>
      <StyledRectangle106706 />
      <StyledHigh>High</StyledHigh>
    </StyledMaskGroup>
    <StyledMaskGroup01>
      <StyledRectangle1074 />
      <StyledRectangle106607 />
    </StyledMaskGroup01>
    <StyledRectangle1076 />
    <StyledWriteamessage>Write a message</StyledWriteamessage>
    <StyledVuesaxbulklampon>
      <StyledVector150 />
      <StyledVector151 />
      <StyledVector152 />
    </StyledVuesaxbulklampon>
    <StyledThoughtsTime>Thoughts Time</StyledThoughtsTime>
    <StyledWedonthaveanynoticeforyoutillthenyoucanshareyourthoughtswithyourpeers>We don’t have any notice for you, till then you can share your thoughts with your peers.</StyledWedonthaveanynoticeforyoutillthenyoucanshareyourthoughtswithyourpeers>
    <StyledVuesaxbulkaddsquare>
      <StyledVector153 />
      <StyledVector154 />
      <StyledVector155 />
    </StyledVuesaxbulkaddsquare>
    <StyledGroup652>
      <StyledRectangle106608 />
      <StyledVuesaxlinearfolder206>
        <StyledVector156 />
        <StyledVector157 />
        <StyledVector158 />
      </StyledVuesaxlinearfolder206>
      <StyledVuesaxoutlinemessage06>
        <StyledVector160 />
        <StyledVector161 />
        <StyledVector162 />
        <StyledVector163 />
        <StyledVector164 />
      </StyledVuesaxoutlinemessage06>
      <Styled10comments>10 comments</Styled10comments>
      <Styled3files>3 files</Styled3files>
      <StyledEllipse1306  src="https://placehold.co/25x24"/>
      <StyledEllipse1207  src="https://placehold.co/24x24"/>
      <StyledResearch>Research</StyledResearch>
      <Styled07>. . .</Styled07>
      <StyledUserresearchhelpsyoutocreateanoptimalproductforusers>User research helps you to create an optimal product for users.</StyledUserresearchhelpsyoutocreateanoptimalproductforusers>
      <StyledRectangle106707 />
      <StyledHigh01>High</StyledHigh01>
    </StyledGroup652>
    <StyledCursorGrabbed>
      <StyledShape />
      <StyledShape01 />
      <StyledShape02 />
      <StyledShape03 />
      <StyledShape04 />
    </StyledCursorGrabbed>
    <StyledMaskGroup02>
      <StyledRectangle1061 />
      <StyledImage1  src="https://placehold.co/47x71"/>
    </StyledMaskGroup02>
  );
};