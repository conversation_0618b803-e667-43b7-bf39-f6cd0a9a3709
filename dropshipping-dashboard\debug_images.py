#!/usr/bin/env python3

import requests
import json

# Get workspace products
response = requests.get('http://localhost:5000/api/workspace/products')
data = response.json()

if data['success'] and data['data']['products']:
    product = data['data']['products'][0]
    print(f"Product ID: {product['_id']}")
    print(f"Title: {product['title']}")
    print(f"Images count: {len(product.get('images', []))}")
    print()
    
    for i, img in enumerate(product.get('images', [])):
        print(f"Image {i+1}:")
        print(f"  URL: {img.get('url', 'No URL')}")
        print(f"  Local path: {img.get('local_path', 'No path')}")
        
        # Test if the image URL works
        if img.get('url'):
            url = img['url']
            if url.startswith('/api/'):
                full_url = f"http://localhost:5000{url}"
            else:
                full_url = f"http://localhost:5000/api/images/images/{url}"
            
            try:
                img_response = requests.get(full_url)
                print(f"  Test URL: {full_url}")
                print(f"  Status: {img_response.status_code}")
                if img_response.status_code == 200:
                    print(f"  ✅ Image loads successfully")
                else:
                    print(f"  ❌ Image failed to load")
            except Exception as e:
                print(f"  ❌ Error testing image: {e}")
        print()
else:
    print("No products found in workspace")
