import os
import requests
from dotenv import load_dotenv
import base64

# Load environment variables from .env file
load_dotenv()

SHOPIFY_API_KEY = os.getenv('SHOPIFY_API_KEY')
SHOPIFY_API_SECRET = os.getenv('SHOPIFY_API_SECRET')
SHOPIFY_STORE_URL = os.getenv('SHOPIFY_STORE_URL')

print(f"Debug - API Key: {SHOPIFY_API_KEY}")
print(f"Debug - Store URL: {SHOPIFY_STORE_URL}")
print(f"Debug - API Secret (masked): {'*' * len(SHOPIFY_API_SECRET) if SHOPIFY_API_SECRET else 'None'}")


def get_auth_headers_access_token():
    """Create authentication headers using X-Shopify-Access-Token"""
    return {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_API_SECRET
    }


def get_auth_headers_basic():
    """Create authentication headers using Basic Auth"""
    credentials = base64.b64encode(f"{SHOPIFY_API_KEY}:{SHOPIFY_API_SECRET}".encode()).decode()
    return {
        "Content-Type": "application/json",
        "Authorization": f"Basic {credentials}"
    }


def format_store_url(store_url):
    """Format store URL to proper Shopify format"""
    if not store_url:
        return None
    
    # If it's already a full myshopify.com URL, use it
    if '.myshopify.com' in store_url:
        return store_url
    
    # If it's just the store name, format it
    if '.' not in store_url:
        return f"{store_url}.myshopify.com"
    
    # If it's a custom domain, try both formats
    return store_url


def test_connection_with_auth(headers, auth_method):
    """Test connection with specific authentication method"""
    try:
        # Format store URL properly
        store_url = format_store_url(SHOPIFY_STORE_URL)
        print(f"\nTesting with {auth_method} authentication...")
        print(f"Using store URL: {store_url}")
        
        # Try different URL formats
        urls_to_try = [
            f"https://{store_url}/admin/api/2023-10/products.json",
            f"https://{SHOPIFY_STORE_URL}.myshopify.com/admin/api/2023-10/products.json" if not '.myshopify.com' in SHOPIFY_STORE_URL else None
        ]
        
        for url in urls_to_try:
            if url is None:
                continue
                
            print(f"Trying URL: {url}")
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                products = response.json()["products"]
                print(f"✅ SUCCESS: Retrieved {len(products)} products using {auth_method}")
                
                if products:
                    print(f"\nFirst product: {products[0]['title']} (ID: {products[0]['id']})")
                    return True, products, url
            else:
                print(f"❌ Failed with status {response.status_code}: {response.text[:200]}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error with {auth_method}: {e}")
    
    return False, None, None


def test_product_operations(products, base_url, headers):
    """Test product update and status change operations"""
    if not products:
        print("No products available for testing operations")
        return
        
    try:
        product = products[0]
        product_id = product["id"]
        original_title = product["title"]
        original_status = product["status"]
        
        print(f"\n=== Testing Product Operations ===")
        print(f"Product ID: {product_id}")
        print(f"Original Title: {original_title}")
        print(f"Original Status: {original_status}")
        
        # Test 1: Update product title
        new_title = f"TEST - {original_title}"
        update_data = {
            "product": {
                "title": new_title
            }
        }
        
        update_url = base_url.replace('/products.json', f'/products/{product_id}.json')
        print(f"Updating product with URL: {update_url}")
        print(f"Payload for update: {update_data}")
        response = requests.put(update_url, headers=headers, json=update_data)
        
        if response.status_code == 200:
            print(f"✅ Product title updated successfully to: {new_title}")
        else:
            print(f"❌ Failed to update title: {response.status_code} - {response.text[:200]}")
            return
        
        # Test 2: Change product status
        new_status = "draft" if original_status == "active" else "active"
        status_data = {
            "product": {
                "status": new_status
            }
        }
        
        response = requests.put(update_url, headers=headers, json=status_data)
        
        if response.status_code == 200:
            print(f"✅ Product status changed successfully to: {new_status}")
        else:
            print(f"❌ Failed to change status: {response.status_code} - {response.text[:200]}")
            return
            
        # Test 3: Restore original values
        restore_data = {
            "product": {
                "title": original_title,
                "status": original_status
            }
        }
        
        response = requests.put(update_url, headers=headers, json=restore_data)
        
        if response.status_code == 200:
            print(f"✅ Product restored to original state successfully")
        else:
            print(f"⚠️ Warning: Failed to restore original state: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during product operations: {e}")


def test_shopify_connection():
    """Test connection to the Shopify API and basic operations."""
    print("=== Shopify API Connection Test ===")
    
    if not all([SHOPIFY_API_KEY, SHOPIFY_API_SECRET, SHOPIFY_STORE_URL]):
        print("❌ Missing required environment variables")
        return
    
    # Test with Access Token method first
    success, products, working_url = test_connection_with_auth(
        get_auth_headers_access_token(), 
        "Access Token"
    )
    
    if not success:
        # Test with Basic Auth method
        success, products, working_url = test_connection_with_auth(
            get_auth_headers_basic(), 
            "Basic Auth"
        )
    
    if success:
        # Test product operations with the working authentication
        headers = get_auth_headers_access_token() if 'Access Token' in str(success) else get_auth_headers_basic()
        test_product_operations(products, working_url, get_auth_headers_access_token())
    else:
        print("\n❌ All authentication methods failed. Please check:")
        print("1. API credentials are correct")
        print("2. Store URL is correct")
        print("3. API permissions are properly configured")
        print("4. Store is accessible")


if __name__ == "__main__":
    test_shopify_connection()

