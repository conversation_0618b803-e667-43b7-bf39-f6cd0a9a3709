Defaulting to user installation because normal site-packages is not writeable
Collecting cloudinary
  Downloading cloudinary-1.44.1-py3-none-any.whl.metadata (8.0 kB)
Requirement already satisfied: six in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from cloudinary) (1.17.0)
Requirement already satisfied: urllib3>=1.26.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from cloudinary) (2.5.0)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from cloudinary) (2025.6.15)
Downloading cloudinary-1.44.1-py3-none-any.whl (147 kB)
Installing collected packages: cloudinary
Successfully installed cloudinary-1.44.1
