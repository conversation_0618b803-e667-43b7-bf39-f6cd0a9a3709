# PowerShell script to test CSV export
$uri = "http://localhost:5000/api/products/export"

# Create request body with product IDs
$body = @{
    format = "csv"
    include_images = $true
    product_ids = @(
        "6884d76576dcaf567a7bac2b",
        "6884d76576dcaf567a7bac29",
        "6884d76476dcaf567a7bac27"
    )
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Body $body -ContentType "application/json"
    
    if ($response -is [string]) {
        # Save CSV content to file
        $response | Out-File -FilePath "exported-products.csv" -Encoding UTF8
        Write-Host "CSV export successful! Saved to exported-products.csv"
        Write-Host "First few lines:"
        Get-Content "exported-products.csv" | Select-Object -First 5
    } else {
        Write-Host "Export response:"
        $response | ConvertTo-Json -Depth 3
    }
} catch {
    Write-Host "Export failed:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody"
    }
}
