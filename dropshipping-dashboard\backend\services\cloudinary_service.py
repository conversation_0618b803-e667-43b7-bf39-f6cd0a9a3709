"""
Cloudinary Service for image upload, management and deletion
"""
import os
import logging
import cloudinary
import cloudinary.uploader
import cloudinary.api
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class CloudinaryService:
    """Service for managing images on Cloudinary"""
    
    def __init__(self):
        """Initialize Cloudinary with credentials from envpass.txt"""
        # Configure Cloudinary from environment variable
        cloudinary_url = os.getenv('CLOUDINARY_URL', 'cloudinary://865235574489374:KH9BtTnnDDPKfTd2c7RzPcBJpdA@dylyjkwms')
        
        if cloudinary_url:
            # Parse the URL to extract credentials
            # Format: cloudinary://api_key:api_secret@cloud_name
            cloudinary.config(cloudinary_url=cloudinary_url)
            logger.info("Cloudinary configured successfully")
        else:
            logger.error("Cloudinary URL not found in environment variables")
            raise ValueError("Cloudinary configuration missing")
    
    def upload_image(self, image_path: str, public_id: str = None, folder: str = None) -> Dict:
        """
        Upload an image to Cloudinary
        
        Args:
            image_path: Local path to the image file
            public_id: Custom public ID for the image (optional)
            folder: Cloudinary folder to organize images (optional)
        
        Returns:
            Dict with upload result containing cloudinary_url, public_id, etc.
        """
        try:
            if not os.path.exists(image_path):
                return {'success': False, 'error': f'Image file not found: {image_path}'}
            
            # Prepare upload options
            upload_options = {
                'use_filename': True,
                'unique_filename': True,
                'overwrite': False,
                'resource_type': 'auto',
                'quality': 'auto:good',  # Automatic quality optimization
                'fetch_format': 'auto'   # Automatic format optimization
            }
            
            if public_id:
                upload_options['public_id'] = public_id
            
            if folder:
                upload_options['folder'] = folder
            
            # Upload to Cloudinary
            logger.info(f"Uploading image to Cloudinary: {image_path}")
            result = cloudinary.uploader.upload(image_path, **upload_options)
            
            return {
                'success': True,
                'cloudinary_url': result.get('secure_url'),
                'public_id': result.get('public_id'),
                'url': result.get('url'),
                'format': result.get('format'),
                'width': result.get('width'),
                'height': result.get('height'),
                'bytes': result.get('bytes'),
                'created_at': result.get('created_at')
            }
            
        except Exception as e:
            logger.error(f"Error uploading image to Cloudinary: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_image(self, public_id: str) -> Dict:
        """
        Delete an image from Cloudinary
        
        Args:
            public_id: The public ID of the image to delete
        
        Returns:
            Dict with deletion result
        """
        try:
            logger.info(f"Deleting image from Cloudinary: {public_id}")
            result = cloudinary.uploader.destroy(public_id)
            
            if result.get('result') == 'ok':
                return {'success': True, 'message': f'Image {public_id} deleted successfully'}
            else:
                return {'success': False, 'error': f'Failed to delete image: {result}'}
                
        except Exception as e:
            logger.error(f"Error deleting image from Cloudinary: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_images_by_prefix(self, prefix: str) -> Dict:
        """
        Delete multiple images by prefix (e.g., product_id)
        
        Args:
            prefix: Prefix to match for bulk deletion
        
        Returns:
            Dict with deletion results
        """
        try:
            logger.info(f"Bulk deleting images with prefix: {prefix}")
            
            # Get list of images with the prefix
            resources = cloudinary.api.resources(
                type='upload',
                prefix=prefix,
                max_results=500  # Cloudinary limit
            )
            
            deleted_count = 0
            errors = []
            
            for resource in resources.get('resources', []):
                public_id = resource['public_id']
                delete_result = self.delete_image(public_id)
                
                if delete_result['success']:
                    deleted_count += 1
                else:
                    errors.append(f"Failed to delete {public_id}: {delete_result['error']}")
            
            return {
                'success': True,
                'deleted_count': deleted_count,
                'errors': errors,
                'message': f'Deleted {deleted_count} images with prefix {prefix}'
            }
            
        except Exception as e:
            logger.error(f"Error bulk deleting images: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_all_images(self) -> Dict:
        """
        Delete ALL images from Cloudinary - USE WITH EXTREME CAUTION
        
        Returns:
            Dict with deletion results
        """
        try:
            logger.warning("DANGEROUS: Attempting to delete ALL images from Cloudinary")
            
            # Get all resources
            resources = cloudinary.api.resources(
                type='upload',
                max_results=500
            )
            
            deleted_count = 0
            errors = []
            
            # Delete in batches
            while resources.get('resources'):
                public_ids = [resource['public_id'] for resource in resources['resources']]
                
                if public_ids:
                    try:
                        # Bulk delete (up to 100 at a time)
                        batch_size = 100
                        for i in range(0, len(public_ids), batch_size):
                            batch = public_ids[i:i + batch_size]
                            result = cloudinary.api.delete_resources(batch)
                            
                            # Count successful deletions
                            for public_id, status in result.get('deleted', {}).items():
                                if status == 'deleted':
                                    deleted_count += 1
                                else:
                                    errors.append(f"Failed to delete {public_id}: {status}")
                    
                    except Exception as batch_error:
                        errors.append(f"Batch deletion error: {str(batch_error)}")
                
                # Get next batch if there are more
                if 'next_cursor' in resources:
                    resources = cloudinary.api.resources(
                        type='upload',
                        max_results=500,
                        next_cursor=resources['next_cursor']
                    )
                else:
                    break
            
            return {
                'success': True,
                'deleted_count': deleted_count,
                'errors': errors,
                'message': f'Deleted {deleted_count} images from Cloudinary'
            }
            
        except Exception as e:
            logger.error(f"Error deleting all images: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_image_info(self, public_id: str) -> Dict:
        """
        Get information about an image
        
        Args:
            public_id: The public ID of the image
        
        Returns:
            Dict with image information
        """
        try:
            result = cloudinary.api.resource(public_id)
            
            return {
                'success': True,
                'data': {
                    'public_id': result.get('public_id'),
                    'url': result.get('secure_url'),
                    'format': result.get('format'),
                    'width': result.get('width'),
                    'height': result.get('height'),
                    'bytes': result.get('bytes'),
                    'created_at': result.get('created_at'),
                    'folder': result.get('folder', '')
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting image info: {e}")
            return {'success': False, 'error': str(e)}

    def remove_background(self, public_id: str, new_public_id: str = None) -> Dict:
        """
        Remove background from an image using Cloudinary's AI background removal

        Args:
            public_id: The public ID of the source image
            new_public_id: Optional public ID for the processed image

        Returns:
            Dict with processing result
        """
        try:
            if not new_public_id:
                new_public_id = f"{public_id}_no_bg"

            logger.info(f"Removing background from image: {public_id}")

            # Use Cloudinary's background removal transformation
            result = cloudinary.uploader.upload(
                f"https://res.cloudinary.com/{cloudinary.config().cloud_name}/image/upload/{public_id}",
                public_id=new_public_id,
                transformation=[
                    {'effect': 'background_removal'},
                    {'format': 'png'}  # PNG to preserve transparency
                ],
                overwrite=True,
                resource_type='image'
            )

            return {
                'success': True,
                'cloudinary_url': result.get('secure_url'),
                'public_id': result.get('public_id'),
                'url': result.get('url'),
                'format': result.get('format'),
                'width': result.get('width'),
                'height': result.get('height'),
                'bytes': result.get('bytes'),
                'created_at': result.get('created_at'),
                'message': f'Background removed successfully for {public_id}'
            }

        except Exception as e:
            logger.error(f"Error removing background with Cloudinary: {e}")
            return {'success': False, 'error': str(e)}

    def remove_background_from_url(self, image_url: str, public_id: str) -> Dict:
        """
        Remove background from an image URL using Cloudinary's AI background removal

        Args:
            image_url: URL of the source image
            public_id: Public ID for the processed image

        Returns:
            Dict with processing result
        """
        try:
            logger.info(f"Removing background from URL: {image_url}")

            # Upload image with background removal transformation
            result = cloudinary.uploader.upload(
                image_url,
                public_id=public_id,
                transformation=[
                    {'effect': 'background_removal'},
                    {'format': 'png'}  # PNG to preserve transparency
                ],
                overwrite=True,
                resource_type='image'
            )

            return {
                'success': True,
                'cloudinary_url': result.get('secure_url'),
                'public_id': result.get('public_id'),
                'url': result.get('url'),
                'format': result.get('format'),
                'width': result.get('width'),
                'height': result.get('height'),
                'bytes': result.get('bytes'),
                'created_at': result.get('created_at'),
                'message': f'Background removed successfully from URL'
            }

        except Exception as e:
            logger.error(f"Error removing background from URL with Cloudinary: {e}")
            return {'success': False, 'error': str(e)}
    
    def extract_public_id_from_url(self, cloudinary_url: str) -> str:
        """
        Extract public_id from Cloudinary URL for deletion
        
        Args:
            cloudinary_url: Full Cloudinary URL
        
        Returns:
            The public_id extracted from the URL
        """
        try:
            # Cloudinary URLs format: https://res.cloudinary.com/cloud_name/image/upload/v123/folder/filename.ext
            # We need to extract everything after the version number
            if '/upload/' in cloudinary_url:
                parts = cloudinary_url.split('/upload/')
                if len(parts) > 1:
                    # Get everything after upload/ and remove file extension
                    path_part = parts[1]
                    # Remove version if present (v1234567890/)
                    if path_part.startswith('v') and '/' in path_part:
                        version_end = path_part.find('/')
                        if version_end > 0:
                            path_part = path_part[version_end + 1:]
                    
                    # Remove file extension
                    if '.' in path_part:
                        path_part = path_part.rsplit('.', 1)[0]
                    
                    return path_part
            
            return cloudinary_url  # Fallback
            
        except Exception as e:
            logger.error(f"Error extracting public_id from URL: {e}")
            return cloudinary_url

# Global instance
cloudinary_service = CloudinaryService()
