/**
 * Shopify Store Management JavaScript
 * Manages products directly from your Shopify store with bulk actions
 */

class ShopifyStoreManager {
    constructor() {
        this.baseURL = 'http://localhost:5000/api';
        this.selectedProducts = new Set();
        this.allProducts = [];
        this.filteredProducts = [];
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadShopifyProducts();
    }

    setupEventListeners() {
        // Refresh button
        document.getElementById('refresh-btn')?.addEventListener('click', () => {
            this.loadShopifyProducts();
        });

        // Search functionality
        document.getElementById('search-input')?.addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });

        // Select all checkbox
        document.getElementById('select-all-checkbox')?.addEventListener('change', (e) => {
            this.selectAllProducts(e.target.checked);
        });

        // Individual product selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('.product-checkbox')) {
                this.toggleProductSelection(e.target.dataset.productId, e.target.checked);
            }
        });

        // Bulk actions
        document.getElementById('select-all-btn')?.addEventListener('click', () => {
            this.selectAllProducts(true);
        });

        document.getElementById('clear-selection-btn')?.addEventListener('click', () => {
            this.selectAllProducts(false);
        });

        document.getElementById('bulk-update-btn')?.addEventListener('click', () => {
            this.bulkUpdateProducts();
        });
    }

    async loadShopifyProducts() {
        this.showLoading();
        
        try {
            const response = await fetch(`${this.baseURL}/shopify/products?limit=250`);
            const data = await response.json();
            
            if (data.success) {
                this.allProducts = data.products || [];
                this.filteredProducts = [...this.allProducts];
                this.renderProductsTable();
                this.showNotification(`Loaded ${this.allProducts.length} products from Shopify`, 'success');
            } else {
                this.showNotification(data.error || 'Failed to load products', 'error');
                this.showEmptyState();
            }
        } catch (error) {
            console.error('Error loading Shopify products:', error);
            this.showNotification('Error connecting to Shopify', 'error');
            this.showEmptyState();
        } finally {
            this.hideLoading();
        }
    }

    renderProductsTable() {
        const tbody = document.getElementById('products-tbody');
        const table = document.getElementById('products-table');
        const emptyState = document.getElementById('empty-state');

        if (!this.filteredProducts || this.filteredProducts.length === 0) {
            table.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        table.style.display = 'table';
        emptyState.style.display = 'none';

        tbody.innerHTML = this.filteredProducts.map(product => this.createProductRow(product)).join('');
        this.updateSelectionCount();
    }

    createProductRow(product) {
        const productId = product.id;
        const mainImage = product.images && product.images.length > 0 
            ? product.images[0].src 
            : 'https://via.placeholder.com/60x60?text=No+Image';
        const title = product.title || 'Untitled Product';
        const price = product.variants && product.variants.length > 0 
            ? parseFloat(product.variants[0].price).toFixed(2)
            : '0.00';
        const inventory = product.variants && product.variants.length > 0 
            ? product.variants[0].inventory_quantity || 0
            : 0;
        const status = product.status || 'unknown';

        return `
            <tr data-product-id="${productId}">
                <td>
                    <input type="checkbox" class="product-checkbox select-checkbox" 
                           data-product-id="${productId}" 
                           ${this.selectedProducts.has(productId.toString()) ? 'checked' : ''}>
                </td>
                <td>
                    <img src="${mainImage}" alt="${title}" class="product-image" 
                         onerror="this.src='https://via.placeholder.com/60x60?text=No+Image'">
                </td>
                <td>
                    <div class="product-title" title="${title}">${title}</div>
                </td>
                <td>
                    <div class="product-price">$${price}</div>
                </td>
                <td>
                    <div class="product-inventory">${inventory}</div>
                </td>
                <td>
                    <span class="status-badge status-${status}">${status}</span>
                </td>
                <td>
                    <div class="product-actions">
                        <button class="action-btn edit" onclick="window.shopifyManager.editProduct('${productId}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <a href="https://shoprave.us/admin/products/${productId}" 
                           target="_blank" class="action-btn view">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </td>
            </tr>
        `;
    }

    selectAllProducts(checked) {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const productId = checkbox.dataset.productId;
            
            if (checked) {
                this.selectedProducts.add(productId);
            } else {
                this.selectedProducts.delete(productId);
            }
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checked;
        }

        this.updateSelectionCount();
    }

    toggleProductSelection(productId, checked) {
        if (checked) {
            this.selectedProducts.add(productId);
        } else {
            this.selectedProducts.delete(productId);
        }

        // Update select all checkbox
        const allCheckboxes = document.querySelectorAll('.product-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
        }

        this.updateSelectionCount();
    }

    updateSelectionCount() {
        const countElement = document.getElementById('selection-count');
        const count = this.selectedProducts.size;
        
        if (countElement) {
            if (count > 0) {
                countElement.textContent = `${count} product${count === 1 ? '' : 's'} selected`;
                countElement.style.display = 'inline';
            } else {
                countElement.textContent = '';
                countElement.style.display = 'none';
            }
        }
    }

    async bulkUpdateProducts() {
        if (this.selectedProducts.size === 0) {
            this.showNotification('Please select products to update', 'warning');
            return;
        }

        const priceMarkup = parseFloat(document.getElementById('price-markup').value) || 0;
        const inventoryAmount = parseInt(document.getElementById('inventory-amount').value) || 0;

        if (priceMarkup === 0 && inventoryAmount === 0) {
            this.showNotification('Please enter price markup % or inventory amount', 'warning');
            return;
        }

        const button = document.getElementById('bulk-update-btn');
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

        try {
            let successCount = 0;
            let errorCount = 0;
            const selectedProductIds = Array.from(this.selectedProducts);

            for (const productId of selectedProductIds) {
                try {
                    await this.updateSingleProduct(productId, priceMarkup, inventoryAmount);
                    successCount++;
                } catch (error) {
                    console.error(`Error updating product ${productId}:`, error);
                    errorCount++;
                }

                // Add small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            if (successCount > 0) {
                this.showNotification(
                    `Successfully updated ${successCount} product${successCount === 1 ? '' : 's'}${errorCount > 0 ? `, ${errorCount} failed` : ''}`, 
                    successCount > errorCount ? 'success' : 'warning'
                );
                
                // Refresh the product list
                await this.loadShopifyProducts();
                
                // Clear selection
                this.selectAllProducts(false);
                
                // Clear input fields
                document.getElementById('price-markup').value = '';
                document.getElementById('inventory-amount').value = '';
            } else {
                this.showNotification('Failed to update any products', 'error');
            }

        } catch (error) {
            console.error('Bulk update error:', error);
            this.showNotification('Error during bulk update', 'error');
        } finally {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    async updateSingleProduct(productId, priceMarkup, inventoryAmount) {
        const product = this.allProducts.find(p => p.id.toString() === productId);
        if (!product || !product.variants || product.variants.length === 0) {
            throw new Error('Product not found or has no variants');
        }

        const variant = product.variants[0]; // Update the first variant
        const updates = {};

        // Update price if markup is specified
        if (priceMarkup > 0) {
            const currentPrice = parseFloat(variant.price);
            const newPrice = (currentPrice * (1 + priceMarkup / 100)).toFixed(2);
            updates.price = newPrice;
        }

        // Update inventory if amount is specified
        if (inventoryAmount > 0) {
            updates.inventory_quantity = inventoryAmount;
        }

        if (Object.keys(updates).length === 0) {
            return; // Nothing to update
        }

        // Update the variant via Shopify API
        const response = await fetch(`${this.baseURL}/shopify/variants/${variant.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updates)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Update failed');
        }

        return await response.json();
    }

    editProduct(productId) {
        // For now, just open the product in Shopify admin
        const product = this.allProducts.find(p => p.id.toString() === productId);
        if (product) {
            window.open(`https://shoprave.us/admin/products/${productId}`, '_blank');
        }
    }

    searchProducts(query) {
        if (!query || query.trim().length === 0) {
            this.filteredProducts = [...this.allProducts];
        } else {
            const searchTerm = query.toLowerCase().trim();
            this.filteredProducts = this.allProducts.filter(product => 
                product.title.toLowerCase().includes(searchTerm) ||
                product.product_type.toLowerCase().includes(searchTerm) ||
                product.vendor.toLowerCase().includes(searchTerm)
            );
        }
        
        this.renderProductsTable();
    }

    showLoading() {
        const loadingState = document.getElementById('loading-state');
        const productsTable = document.getElementById('products-table');
        const emptyState = document.getElementById('empty-state');
        
        if (loadingState) loadingState.style.display = 'block';
        if (productsTable) productsTable.style.display = 'none';
        if (emptyState) emptyState.style.display = 'none';
    }

    hideLoading() {
        const loadingState = document.getElementById('loading-state');
        if (loadingState) loadingState.style.display = 'none';
    }

    showEmptyState() {
        const emptyState = document.getElementById('empty-state');
        const productsTable = document.getElementById('products-table');
        
        if (emptyState) emptyState.style.display = 'block';
        if (productsTable) productsTable.style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type} show`;
        notification.innerHTML = `
            <i class="fas ${this.getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        `;
        
        const container = document.getElementById('notifications');
        container.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
        
        // Close button functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        });
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.shopifyManager = new ShopifyStoreManager();
});
