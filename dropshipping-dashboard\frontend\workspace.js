document.addEventListener('DOMContentLoaded', function() {
  loadWorkspaceProducts();

  document.getElementById('reset-statuses-btn').addEventListener('click', resetStatuses);
});

function loadWorkspaceProducts() {
  fetch('/api/shopify/products?status=draft')
    .then(response => response.json())
    .then(data => {
      const tbody = document.getElementById('products-tbody');
      tbody.innerHTML = '';

      if (data.success && data.products.length > 0) {
        data.products.forEach(product => {
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td class="product-images">
              ${product.images.map(img => `<img src="${img.src}" class="product-image" />`).join('')}
            </td>
            <td class="product-title">${product.title}</td>
            <td class="product-description">${product.body_html}</td>
            <td class="product-price">$${product.variants[0].price}</td>
            <td>
              <button class="btn-export" onclick="exportProduct('${product.id}')">Export</button>
              <button class="btn-preview" onclick="previewProduct('${product.id}')">Preview</button>
            </td>
          `;
          tbody.appendChild(tr);
        });
        document.getElementById('loading').style.display = 'none';
        document.getElementById('products-list').style.display = 'block';
      } else {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('empty-state').style.display = 'block';
      }
    })
    .catch(error => {
      console.error('Error loading workspace products:', error);
    });
}

function exportProduct(productId) {
  fetch(`/api/shopify/export-product/${productId}`, {
    method: 'POST',
  })
  .then(response => response.json())
  .then(data => {
    showToast(data.success ? 'success' : 'error', data.message || data.error);
    if (data.success) {
      loadWorkspaceProducts();
    }
  })
  .catch(error => {
    console.error('Error exporting product:', error);
    showToast('error', 'An error occurred while exporting the product.');
  });
}

function previewProduct(productId) {
  fetch(`/api/shopify/products/${productId}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const product = data.product;
        // Here you can open a modal or handle UI for the preview
        // For example:
        console.log('Preview product', product); // Replace with actual preview handling
      } else {
        showToast('error', data.error);
      }
    })
    .catch(error => {
      console.error('Error fetching product details:', error);
      showToast('error', 'An error occurred while fetching the product details.');
    });
}

function resetStatuses() {
  console.log('Resetting statuses...');
  // Handle the reset functionality here
}

function showToast(type, message) {
  const toastContainer = document.getElementById('toast-container');
  const toast = document.createElement('div');
  toast.className = `toast toast-${type} toast-show`;
  toast.innerHTML = `
    <div class="toast-content">
      <span class="toast-icon">${type === 'success' ? '✅' : '⚠️'}</span>
      <p class="toast-message">${message}</p>
      <button class="toast-close" onclick="closeToast(this)">&times;</button>
    </div>
  `;
  toastContainer.appendChild(toast);
  setTimeout(() => closeToast(toast), 5000);
}

function closeToast(toastElement) {
  if (toastElement.parentElement) {
    toastElement.parentElement.classList.remove('toast-show');
    // Optionally remove the toast from DOM after hiding
    setTimeout(() => toastElement.parentElement.remove(), 300);
  }
}
