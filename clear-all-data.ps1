# PowerShell script to completely clear all data using the API endpoint
$baseUri = "http://localhost:5000/api"

Write-Host "🧹 Clearing ALL data from R.A.V.E Dashboard..." -ForegroundColor Yellow
Write-Host "⚠️  This will delete EVERYTHING in the database!" -ForegroundColor Red

# Create confirmation body
$body = @{
    confirm = "yes"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUri/clear-all-data" -Method Post -Body $body -ContentType "application/json"
    
    if ($response.success) {
        Write-Host "`n✅ Database cleared successfully!" -ForegroundColor Green
        Write-Host "Deleted:" -ForegroundColor Cyan
        Write-Host "  - Products: $($response.deleted.products)" -ForegroundColor White
        Write-Host "  - Orders: $($response.deleted.orders)" -ForegroundColor White  
        Write-Host "  - Import Batches: $($response.deleted.import_batches)" -ForegroundColor White
        Write-Host "`n📁 Ready for real AliExpress data upload!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to clear data: $($response.error)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error clearing data: $($_.Exception.Message)" -ForegroundColor Red
}
