"""
AI Content Service for product title and description rewriting
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone
import openai
from anthropic import Anthropic
import google.generativeai as genai
import requests

from config import Config
from services.mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class AIContentService:
    """Service for AI-powered content rewriting"""
    
    def __init__(self):
        self.config = Config()
        self.openai_client = None
        self.claude_client = None
        self.gemini_client = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize AI service clients"""
        try:
            if self.config.OPENAI_API_KEY:
                openai.api_key = self.config.OPENAI_API_KEY
                self.openai_client = openai

            if self.config.CLAUDE_API_KEY:
                self.claude_client = Anthropic(api_key=self.config.CLAUDE_API_KEY)

            if self.config.GEMINI_API_KEY:
                genai.configure(api_key=self.config.GEMINI_API_KEY)
                self.gemini_client = genai.GenerativeModel('gemini-1.5-flash')

        except Exception as e:
            logger.error(f"Error initializing AI clients: {e}")
    
    def rewrite_title(self, product_id: str, provider: str = "openai", custom_prompt: str = None) -> Dict:
        """Rewrite product title using specified AI provider"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            original_title = product.get('original_content', {}).get('title', '')
            if not original_title:
                return {'success': False, 'error': 'No original title found'}
            
            # Update status to processing
            mongodb_service.update_product(product_id, {
                'ai_content.title.rewrite_status': 'processing',
                'ai_content.title.llm_provider': provider
            })
            
            # Generate rewritten title
            if provider == 'openai':
                result = self._rewrite_with_openai(original_title, 'title', custom_prompt)
            elif provider == 'claude':
                result = self._rewrite_with_claude(original_title, 'title', custom_prompt)
            elif provider == 'gemini':
                result = self._rewrite_with_gemini(original_title, 'title', custom_prompt)
            else:
                return {'success': False, 'error': f'Unsupported provider: {provider}'}
            
            if result['success']:
                # Update product with rewritten content
                update_data = {
                    'ai_content.title.rewritten': result['content'],
                    'ai_content.title.rewrite_status': 'completed',
                    'ai_content.title.rewrite_timestamp': datetime.now(timezone.utc),
                    'ai_content.title.user_approved': False
                }
                
                mongodb_service.update_product(product_id, update_data)
                
                return {
                    'success': True,
                    'rewritten_title': result['content'],
                    'cost': result.get('cost', 0)
                }
            else:
                # Update status to failed
                mongodb_service.update_product(product_id, {
                    'ai_content.title.rewrite_status': 'failed'
                })
                return result
                
        except Exception as e:
            logger.error(f"Error rewriting title: {e}")
            mongodb_service.update_product(product_id, {
                'ai_content.title.rewrite_status': 'failed'
            })
            return {'success': False, 'error': str(e)}
    
    def rewrite_description(self, product_id: str, provider: str = "openai", custom_prompt: str = None) -> Dict:
        """Rewrite product description using specified AI provider"""
        try:
            # Get product
            product = mongodb_service.get_product(product_id)
            if not product:
                return {'success': False, 'error': 'Product not found'}
            
            original_title = product.get('original_content', {}).get('title', '')
            original_description = product.get('original_content', {}).get('description', original_title)
            
            if not original_title:
                return {'success': False, 'error': 'No original content found'}
            
            # Update status to processing
            mongodb_service.update_product(product_id, {
                'ai_content.description.rewrite_status': 'processing',
                'ai_content.description.llm_provider': provider
            })
            
            # Generate rewritten description
            content_context = f"Title: {original_title}\nDescription: {original_description}"
            
            if provider == 'openai':
                result = self._rewrite_with_openai(content_context, 'description', custom_prompt)
            elif provider == 'claude':
                result = self._rewrite_with_claude(content_context, 'description', custom_prompt)
            elif provider == 'gemini':
                result = self._rewrite_with_gemini(content_context, 'description', custom_prompt)
            else:
                return {'success': False, 'error': f'Unsupported provider: {provider}'}
            
            if result['success']:
                # Extract SEO keywords
                seo_keywords = self._extract_seo_keywords(result['content'])
                
                # Update product with rewritten content
                update_data = {
                    'ai_content.description.rewritten': result['content'],
                    'ai_content.description.seo_keywords': seo_keywords,
                    'ai_content.description.rewrite_status': 'completed',
                    'ai_content.description.rewrite_timestamp': datetime.now(timezone.utc),
                    'ai_content.description.user_approved': False
                }
                
                mongodb_service.update_product(product_id, update_data)
                
                return {
                    'success': True,
                    'rewritten_description': result['content'],
                    'seo_keywords': seo_keywords,
                    'cost': result.get('cost', 0)
                }
            else:
                # Update status to failed
                mongodb_service.update_product(product_id, {
                    'ai_content.description.rewrite_status': 'failed'
                })
                return result
                
        except Exception as e:
            logger.error(f"Error rewriting description: {e}")
            mongodb_service.update_product(product_id, {
                'ai_content.description.rewrite_status': 'failed'
            })
            return {'success': False, 'error': str(e)}
    
    def bulk_rewrite(self, product_ids: List[str], content_type: str, provider: str = "openai") -> Dict:
        """Bulk rewrite content for multiple products"""
        results = {
            'success': True,
            'processed_count': 0,
            'total_requested': len(product_ids),
            'errors': [],
            'total_cost': 0
        }
        
        try:
            for product_id in product_ids:
                try:
                    if content_type in ['title', 'both']:
                        title_result = self.rewrite_title(product_id, provider)
                        if title_result['success']:
                            results['total_cost'] += title_result.get('cost', 0)
                        else:
                            results['errors'].append(f"Title rewrite failed for {product_id}: {title_result['error']}")
                    
                    if content_type in ['description', 'both']:
                        desc_result = self.rewrite_description(product_id, provider)
                        if desc_result['success']:
                            results['total_cost'] += desc_result.get('cost', 0)
                        else:
                            results['errors'].append(f"Description rewrite failed for {product_id}: {desc_result['error']}")
                    
                    results['processed_count'] += 1
                    
                except Exception as e:
                    results['errors'].append(f"Error processing {product_id}: {str(e)}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in bulk rewrite: {e}")
            results['success'] = False
            results['errors'].append(f"Bulk processing error: {str(e)}")
            return results
    
    def _rewrite_with_openai(self, content: str, content_type: str, custom_prompt: str = None) -> Dict:
        """Rewrite content using OpenAI"""
        try:
            if not self.openai_client:
                return {'success': False, 'error': 'OpenAI client not initialized'}
            
            # Get prompt template
            if custom_prompt:
                prompt = custom_prompt
            else:
                prompt = self._get_prompt_template(content_type)
            
            # Make API call
            response = openai.ChatCompletion.create(
                model=self.config.LLM_SETTINGS['openai']['model'],
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": content}
                ],
                temperature=self.config.LLM_SETTINGS['openai']['temperature'],
                max_tokens=self.config.LLM_SETTINGS['openai']['max_tokens']
            )
            
            rewritten_content = response.choices[0].message.content.strip()
            
            # Calculate cost (approximate)
            cost = self._calculate_openai_cost(response.usage.total_tokens)
            
            return {
                'success': True,
                'content': rewritten_content,
                'cost': cost
            }
            
        except Exception as e:
            logger.error(f"Error with OpenAI rewrite: {e}")
            return {'success': False, 'error': str(e)}
    
    def _rewrite_with_claude(self, content: str, content_type: str, custom_prompt: str = None) -> Dict:
        """Rewrite content using Claude"""
        try:
            if not self.claude_client:
                return {'success': False, 'error': 'Claude client not initialized'}
            
            # Get prompt template
            if custom_prompt:
                prompt = custom_prompt
            else:
                prompt = self._get_prompt_template(content_type)
            
            # Make API call
            response = self.claude_client.messages.create(
                model=self.config.LLM_SETTINGS['claude']['model'],
                max_tokens=self.config.LLM_SETTINGS['claude']['max_tokens'],
                temperature=self.config.LLM_SETTINGS['claude']['temperature'],
                messages=[
                    {"role": "user", "content": f"{prompt}\n\n{content}"}
                ]
            )
            
            rewritten_content = response.content[0].text.strip()
            
            # Calculate cost (approximate)
            cost = self._calculate_claude_cost(len(content), len(rewritten_content))
            
            return {
                'success': True,
                'content': rewritten_content,
                'cost': cost
            }
            
        except Exception as e:
            logger.error(f"Error with Claude rewrite: {e}")
            return {'success': False, 'error': str(e)}

    def _rewrite_with_gemini(self, content: str, content_type: str, custom_prompt: str = None) -> Dict:
        """Rewrite content using Google Gemini"""
        try:
            if not self.gemini_client:
                return {'success': False, 'error': 'Gemini client not initialized'}

            # Get prompt template
            if custom_prompt:
                prompt = f"{custom_prompt}\n\nContent to rewrite: {content}"
            else:
                template = self._get_prompt_template(content_type, custom_prompt)
                prompt = f"{template}\n\nContent to rewrite: {content}"

            # Generate content
            response = self.gemini_client.generate_content(prompt)

            if not response.text:
                return {'success': False, 'error': 'No response from Gemini'}

            rewritten_content = response.text.strip()

            # Calculate cost (approximate - Gemini pricing)
            cost = self._calculate_gemini_cost(len(content), len(rewritten_content))

            return {
                'success': True,
                'content': rewritten_content,
                'cost': cost
            }

        except Exception as e:
            logger.error(f"Error with Gemini rewrite: {e}")
            return {'success': False, 'error': str(e)}

    def rewrite_title_direct(self, content: str, provider: str = "gemini", style: str = None) -> Dict:
        """Directly rewrite title content without database operations"""
        try:
            if provider == "openai":
                return self._rewrite_with_openai(content, 'title', style)
            elif provider == "claude":
                return self._rewrite_with_claude(content, 'title', style)
            elif provider == "gemini":
                return self._rewrite_with_gemini(content, 'title', style)
            else:
                return {'success': False, 'error': f'Unsupported provider: {provider}'}
        except Exception as e:
            logger.error(f"Error in direct title rewrite: {e}")
            return {'success': False, 'error': str(e)}

    def rewrite_description_direct(self, content: str, provider: str = "gemini", style: str = None) -> Dict:
        """Directly rewrite description content without database operations"""
        try:
            if provider == "openai":
                return self._rewrite_with_openai(content, 'description', style)
            elif provider == "claude":
                return self._rewrite_with_claude(content, 'description', style)
            elif provider == "gemini":
                return self._rewrite_with_gemini(content, 'description', style)
            else:
                return {'success': False, 'error': f'Unsupported provider: {provider}'}
        except Exception as e:
            logger.error(f"Error in direct description rewrite: {e}")
            return {'success': False, 'error': str(e)}

    def _get_prompt_template(self, content_type: str, style: str = None) -> str:
        """Get prompt template for content type"""
        if content_type == 'title':
            if style and 'AUNTY DIVA' in style:
                return """You are an expert luxury e-commerce copywriter. Create a SHORT product title (under 60 characters) in AUNTY DIVA style.

REQUIREMENTS:
- Use ALL CAPS format like: "AUNTY DIVA CHUNKY HOOPS EARRINGS"
- Include "AUNTY DIVA" brand name
- Add 1-2 descriptive words (chunky, luxurious, premium, etc.)
- Keep it under 60 characters total
- Focus on the main product type

Examples:
- "AUNTY DIVA CHUNKY HOOPS EARRINGS"
- "AUNTY DIVA PREMIUM GOLD NECKLACE"
- "AUNTY DIVA LUXURY CRYSTAL RING"

Return ONLY the short title, nothing else."""
            else:
                return """You are an expert e-commerce copywriter. Rewrite the following product title to be more appealing, professional, and SEO-friendly for online sales.

Requirements:
- Keep it under 80 characters
- Make it compelling and descriptive
- Include key product features
- Use proper capitalization
- Avoid excessive punctuation or symbols
- Focus on benefits and appeal to customers

Return only the rewritten title, nothing else."""

        elif content_type == 'description':
            if style and 'Premium product description' in style:
                return """You are an expert luxury e-commerce copywriter. Rewrite the following product description in the AUNTY DIVA premium style - luxurious, detailed, and specification-rich.

AUNTY DIVA Style Guidelines:
- Start with an engaging luxury description
- Use words like "Enrich your look", "luxurious yet versatile", "classy touch", "stunningly beautiful"
- Include detailed specifications in a clean format
- Mention premium materials (e.g., "Premium Stainless Steel")
- Highlight quality features (e.g., "No Tarnishing", "Premium Quality")
- Include exact measurements and weight when available
- Use professional formatting with line breaks

Example format:
"Enrich your look with these luxurious yet versatile chunky AUNTY DIVA hoops. Crafted with a rich material, these earrings offer a classy touch to any outfit. Instantly upgrade your wardrobe with these stunningly beautiful hoops.

Premium Stainless Steel
Premium Quality
No Tarnishing
Length 31mm
Width 1.8cm
Thickness 10mm
Weight 11.7g"

Return only the product description in AUNTY DIVA style, nothing else."""
            else:
                return """You are an expert e-commerce copywriter. Create a compelling product description based on the provided title and basic description.

Requirements:
- Write 2-3 paragraphs (150-250 words)
- Highlight key features and benefits
- Use persuasive language that encourages purchase
- Include relevant keywords naturally
- Focus on customer value and problem-solving
- Use proper grammar and formatting
- Make it scannable with clear benefits

Return only the product description, nothing else."""
        
        else:
            return "Rewrite the following content to be more professional and appealing:"
    
    def _extract_seo_keywords(self, content: str) -> List[str]:
        """Extract SEO keywords from content"""
        # Simple keyword extraction - could be enhanced with NLP
        import re
        
        # Remove common words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'}
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        
        # Filter and count
        word_freq = {}
        for word in words:
            if word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top keywords
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        return [word for word, freq in keywords if freq > 1]
    
    def _calculate_openai_cost(self, tokens: int) -> float:
        """Calculate approximate OpenAI cost"""
        # GPT-4 pricing (approximate)
        cost_per_1k_tokens = 0.03
        return (tokens / 1000) * cost_per_1k_tokens
    
    def _calculate_claude_cost(self, input_chars: int, output_chars: int) -> float:
        """Calculate approximate Claude cost"""
        # Claude pricing (approximate)
        input_cost_per_1k = 0.008
        output_cost_per_1k = 0.024
        
        input_cost = (input_chars / 1000) * input_cost_per_1k
        output_cost = (output_chars / 1000) * output_cost_per_1k
        
        return input_cost + output_cost

    def _calculate_gemini_cost(self, input_chars: int, output_chars: int) -> float:
        """Calculate approximate Gemini cost"""
        # Gemini Pro pricing (approximate)
        input_cost_per_1k = 0.0005  # $0.50 per 1M characters
        output_cost_per_1k = 0.0015  # $1.50 per 1M characters

        input_cost = (input_chars / 1000) * input_cost_per_1k
        output_cost = (output_chars / 1000) * output_cost_per_1k

        return input_cost + output_cost

    def get_available_providers(self) -> Dict:
        """Get available AI providers and their status"""
        providers = {
            'openai': {
                'name': 'OpenAI GPT-4',
                'available': bool(self.config.OPENAI_API_KEY),
                'model': self.config.LLM_SETTINGS['openai']['model']
            },
            'claude': {
                'name': 'Claude 3 Sonnet',
                'available': bool(self.config.CLAUDE_API_KEY),
                'model': self.config.LLM_SETTINGS['claude']['model']
            },
            'gemini': {
                'name': 'Google Gemini Pro',
                'available': bool(self.config.GEMINI_API_KEY),
                'model': self.config.LLM_SETTINGS['gemini']['model']
            }
        }
        
        return providers
    
    def test_connection(self, provider: str) -> Dict:
        """Test connection to AI provider"""
        try:
            test_content = "Test product title"
            
            if provider == 'openai':
                result = self._rewrite_with_openai(test_content, 'title')
            elif provider == 'claude':
                result = self._rewrite_with_claude(test_content, 'title')
            elif provider == 'gemini':
                result = self._rewrite_with_gemini(test_content, 'title')
            else:
                return {'success': False, 'error': 'Unknown provider'}
            
            if result['success']:
                return {
                    'success': True,
                    'message': f'{provider} connection successful',
                    'test_response': result['content'][:50] + '...'
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error testing {provider} connection: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_usage_statistics(self) -> Dict:
        """Get AI usage statistics"""
        try:
            # Get usage from database (this would need to be implemented)
            stats = {
                'total_requests_today': 0,
                'total_requests_month': 0,
                'total_cost_today': 0.0,
                'total_cost_month': 0.0,
                'by_provider': {
                    'openai': {'requests': 0, 'cost': 0.0},
                    'claude': {'requests': 0, 'cost': 0.0}
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting usage statistics: {e}")
            return {}

