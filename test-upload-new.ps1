# Test upload functionality with PowerShell
$uri = "http://localhost:5000/api/upload/json"
$filePath = "download.json"

Write-Host "Testing JSON upload functionality..." -ForegroundColor Green
Write-Host "File: $filePath" -ForegroundColor Yellow
Write-Host "Endpoint: $uri" -ForegroundColor Yellow

try {
    # Create multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    # Read file content
    $fileBytes = [System.IO.File]::ReadAllBytes($filePath)
    $fileEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)
    
    # Create form data
    $bodyLines = (
        "--$boundary",
        "Content-Disposition: form-data; name=`"file`"; filename=`"download.json`"",
        "Content-Type: application/json$LF",
        $fileEnc,
        "--$boundary--$LF"
    ) -join $LF
    
    # Make request
    $response = Invoke-RestMethod -Uri $uri -Method Post -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
    
    Write-Host "Upload successful!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "Upload failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
