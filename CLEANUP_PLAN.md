# Project Cleanup Plan - AliExpress Image Downloader

## 🔧 Files to KEEP (Essential for Python Image Downloader)

### Core Python Files
- `aliexpress_image_downloader.py` - Main Python script (752 lines)
- `requirements.txt` - Python dependencies
- `test_my_url.py` - Test script for single URL testing

### Documentation
- `IMAGE_DOWNLOADER_README.md` - Comprehensive documentation for the image downloader
- `sample_urls.txt` - Sample URLs for testing

### Generated Files (Keep if exists)
- `aliexpress_downloader.log` - Log file (generated during runtime)

## 🗑️ Files and Directories to DELETE

### Browser Extension Directories (4 different extensions) ---> delete
- `aliexpress/` - Contains: manifest.json, popup files, xx1-xx9.js, worker.js, _metadata/, images/
- `aliexpressinventory/` - Contains: options.html, popup.html, service-worker-loader.js, _metadata/, assets/, css/, fonts/, icons/, img/, img-round/, img-sq/, src/
- `Shopper/` - Contains: _metadata/, assets/, css/, fonts/, icons/, img/, img-round/, img-sq/, src/
- `image-downloader/` - Third-party extension: Contains complete browser extension with .gitattributes, .gitignore, .prettierrc, bun.lock, jest.config.js, LICENSE.md, manifest.json, options.png, package.json, README.md, USERGUIDE, images/, lib/, scripts/, src/, stylesheets/

### Test Data and Downloads (5 directories) ---> delete
- `batch_test/` - Contains: 1005005277947991_Aliexpress/, 1005005971268578_Aliexpress/ (with images and metadata.json)
- `my_test_download/` - Contains: 3256806060264024_Aliexpress/, 3256808519179538_Aliexpress/ (with images and metadata.json)
- `test_download/` - Contains: 1005005971268578_Aliexpress/
- `test_download2/` - Contains: 1005005277947991_Aliexpress/
- `test_download3/` - Contains: 1005005277947991_Aliexpress/
- `downloads/` - Empty download directory

### Generic Source Directory -0-> delete
- `src/` - Contains: background/, content/, options/, popup/ (generic structure)

### Miscellaneous Files (10 files) ---> delete
- `manifest.json` - Browser extension manifest (root level)
- `popup.html` - Browser extension popup (root level)
- `popup.js` - Browser extension popup script (root level)
- `images/` - Extension icons directory (AliHelper_icon_gray.png, AliHelper_icon.png, icon-16.png, icon-32.png, icon-48.png, icon-128.png, README.md)
- `download (3).csv` - CSV file
- `DEBUG.md` - Debug documentation
- `INSTALLATION.md` - Installation documentation (redundant with IMAGE_DOWNLOADER_README.md)
- `README.md` - Generic readme (keeping the specific IMAGE_DOWNLOADER_README.md instead)
- `lendqube_key_working` - **KEEP THIS FILE** (moved to keep list)

## 📊 Cleanup Summary

### Files to Keep: 7 files
- 3 Python files
- 2 documentation files
- 1 sample data file
- 1 key file (lendqube_key_working)

### Files/Directories to Delete: 20 items
- 4 browser extension directories
- 6 test/download directories
- 1 generic source directory
- 10 miscellaneous files

### Estimated Space Savings
- Browser extensions: ~50+ MB
- Test downloads: ~10+ MB of sample images
- Miscellaneous: ~5+ MB

## 🎯 Final Project Structure (After Cleanup)
```
aliexpress/
├── aliexpress_image_downloader.py    # Main Python script
├── requirements.txt                  # Dependencies
├── test_my_url.py                   # Test script
├── IMAGE_DOWNLOADER_README.md       # Documentation
├── sample_urls.txt                  # Sample URLs
├── lendqube_key_working             # Key file (kept per user request)
└── aliexpress_downloader.log        # Log file (generated)
```

## ⚠️ Important Notes
- All browser extension functionality will be removed
- All test download data will be deleted
- Only the core Python image downloader will remain
- The project will be focused solely on downloading images from AliExpress URLs