#!/usr/bin/env python3
"""
Check the specific extraction error for the failed product
"""
import requests
import json

def check_extraction_error():
    print("🔍 Checking Image Extraction Error")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    product_id = "68855a804a4c7de804a83a17"  # The bracelet product
    
    try:
        response = requests.get(f"{base_url}/api/products/{product_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                product = result.get('data')
                
                print(f"Product ID: {product['_id']}")
                print(f"Title: {product.get('title', 'N/A')}")
                print(f"Status: {product.get('status', 'N/A')}")
                print(f"AliExpress URL: {product.get('aliexpress_data', {}).get('original_url', 'N/A')}")
                
                # Check for extraction error
                extraction_error = product.get('image_extraction_error')
                if extraction_error:
                    print(f"\n❌ Image Extraction Error:")
                    print(f"   {extraction_error}")
                else:
                    print(f"\n✅ No extraction error found")
                
                # Check images
                images = product.get('images', [])
                print(f"\nImages ({len(images)}):")
                for i, img in enumerate(images):
                    print(f"   {i+1}. URL: {img.get('url', 'N/A')}")
                    print(f"      Local Path: {img.get('local_path', 'N/A')}")
                    print(f"      Download Status: {img.get('download_status', 'N/A')}")
                
            else:
                print(f"❌ API Error: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_extraction_error()
