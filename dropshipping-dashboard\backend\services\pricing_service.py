"""
Pricing Service for calculating and applying markup rules
"""
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone
from services.mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class PricingService:
    """Service for handling product pricing and markup calculations"""
    
    def __init__(self):
        pass
    
    def calculate_markup(self, original_price: float, rule_type: str, markup_value: float) -> Dict:
        """Calculate markup based on rule type"""
        try:
            if rule_type == 'percentage':
                markup_amount = original_price * (markup_value / 100)
                final_price = original_price + markup_amount
                profit_margin = (markup_amount / final_price) * 100 if final_price > 0 else 0
                
            elif rule_type == 'fixed_amount':
                markup_amount = markup_value
                final_price = original_price + markup_amount
                profit_margin = (markup_amount / final_price) * 100 if final_price > 0 else 0
                
            elif rule_type == 'tiered':
                # Tiered pricing based on original price ranges
                if original_price <= 10:
                    markup_percentage = 200  # 200% markup for items under $10
                elif original_price <= 25:
                    markup_percentage = 150  # 150% markup for items $10-25
                elif original_price <= 50:
                    markup_percentage = 100  # 100% markup for items $25-50
                else:
                    markup_percentage = 75   # 75% markup for items over $50
                
                markup_amount = original_price * (markup_percentage / 100)
                final_price = original_price + markup_amount
                profit_margin = (markup_amount / final_price) * 100 if final_price > 0 else 0
                
            else:
                raise ValueError(f"Unknown rule type: {rule_type}")
            
            return {
                'original_price': round(original_price, 2),
                'markup_amount': round(markup_amount, 2),
                'final_price': round(final_price, 2),
                'profit_margin': round(profit_margin, 2),
                'markup_percentage': round((markup_amount / original_price) * 100, 2) if original_price > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error calculating markup: {e}")
            return {
                'original_price': original_price,
                'markup_amount': 0,
                'final_price': original_price,
                'profit_margin': 0,
                'markup_percentage': 0,
                'error': str(e)
            }
    
    def apply_pricing_to_products(self, product_ids: List[str], rule_type: str, markup_value: float) -> Dict:
        """Apply pricing rules to multiple products"""
        results = {
            'success': True,
            'updated_count': 0,
            'total_requested': len(product_ids),
            'errors': [],
            'summary': {
                'total_original_value': 0,
                'total_final_value': 0,
                'total_markup_amount': 0,
                'average_profit_margin': 0
            }
        }
        
        try:
            updated_products = []
            total_profit_margins = []
            
            for product_id in product_ids:
                try:
                    # Get product
                    product = mongodb_service.get_product(product_id)
                    if not product:
                        results['errors'].append(f"Product not found: {product_id}")
                        continue
                    
                    original_price = float(product.get('original_price', 0))
                    if original_price <= 0:
                        results['errors'].append(f"Invalid original price for product: {product_id}")
                        continue
                    
                    # Calculate new pricing
                    pricing_calc = self.calculate_markup(original_price, rule_type, markup_value)
                    
                    if 'error' in pricing_calc:
                        results['errors'].append(f"Pricing calculation error for {product_id}: {pricing_calc['error']}")
                        continue
                    
                    # Update product with new pricing
                    update_data = {
                        'final_price': pricing_calc['final_price'],
                        'markup_percentage': pricing_calc['markup_percentage'],
                        'pricing_applied_at': datetime.now(timezone.utc),
                        'pricing_rule': {
                            'type': rule_type,
                            'value': markup_value,
                            'applied_at': datetime.now(timezone.utc)
                        }
                    }
                    
                    success = mongodb_service.update_product(product_id, update_data)
                    if success:
                        results['updated_count'] += 1
                        updated_products.append({
                            'product_id': product_id,
                            'title': product.get('title', ''),
                            **pricing_calc
                        })
                        
                        # Add to summary calculations
                        results['summary']['total_original_value'] += pricing_calc['original_price']
                        results['summary']['total_final_value'] += pricing_calc['final_price']
                        results['summary']['total_markup_amount'] += pricing_calc['markup_amount']
                        total_profit_margins.append(pricing_calc['profit_margin'])
                        
                    else:
                        results['errors'].append(f"Failed to update product: {product_id}")
                        
                except Exception as e:
                    results['errors'].append(f"Error processing product {product_id}: {str(e)}")
            
            # Calculate average profit margin
            if total_profit_margins:
                results['summary']['average_profit_margin'] = round(
                    sum(total_profit_margins) / len(total_profit_margins), 2
                )
            
            # Round summary values
            for key in ['total_original_value', 'total_final_value', 'total_markup_amount']:
                results['summary'][key] = round(results['summary'][key], 2)
            
            results['updated_products'] = updated_products
            
            return results
            
        except Exception as e:
            logger.error(f"Error applying pricing to products: {e}")
            results['success'] = False
            results['errors'].append(f"Service error: {str(e)}")
            return results
    
    def calculate_pricing_preview(self, product_ids: List[str], rule_type: str, markup_value: float) -> Dict:
        """Calculate pricing preview without applying changes"""
        preview = {
            'success': True,
            'products': [],
            'summary': {
                'total_products': len(product_ids),
                'total_original_value': 0,
                'total_final_value': 0,
                'total_markup_amount': 0,
                'average_profit_margin': 0
            },
            'errors': []
        }
        
        try:
            total_profit_margins = []
            
            for product_id in product_ids:
                try:
                    # Get product
                    product = mongodb_service.get_product(product_id)
                    if not product:
                        preview['errors'].append(f"Product not found: {product_id}")
                        continue
                    
                    original_price = float(product.get('original_price', 0))
                    if original_price <= 0:
                        preview['errors'].append(f"Invalid original price for product: {product_id}")
                        continue
                    
                    # Calculate pricing
                    pricing_calc = self.calculate_markup(original_price, rule_type, markup_value)
                    
                    if 'error' in pricing_calc:
                        preview['errors'].append(f"Calculation error for {product_id}: {pricing_calc['error']}")
                        continue
                    
                    # Add to preview
                    preview['products'].append({
                        'product_id': product_id,
                        'title': product.get('title', ''),
                        'current_final_price': product.get('final_price', original_price),
                        **pricing_calc
                    })
                    
                    # Add to summary
                    preview['summary']['total_original_value'] += pricing_calc['original_price']
                    preview['summary']['total_final_value'] += pricing_calc['final_price']
                    preview['summary']['total_markup_amount'] += pricing_calc['markup_amount']
                    total_profit_margins.append(pricing_calc['profit_margin'])
                    
                except Exception as e:
                    preview['errors'].append(f"Error processing product {product_id}: {str(e)}")
            
            # Calculate average profit margin
            if total_profit_margins:
                preview['summary']['average_profit_margin'] = round(
                    sum(total_profit_margins) / len(total_profit_margins), 2
                )
            
            # Round summary values
            for key in ['total_original_value', 'total_final_value', 'total_markup_amount']:
                preview['summary'][key] = round(preview['summary'][key], 2)
            
            return preview
            
        except Exception as e:
            logger.error(f"Error calculating pricing preview: {e}")
            preview['success'] = False
            preview['errors'].append(f"Service error: {str(e)}")
            return preview
    
    def create_pricing_rule(self, rule_data: Dict) -> Optional[str]:
        """Create a new pricing rule"""
        try:
            rule_record = {
                'rule_name': rule_data.get('rule_name'),
                'rule_type': rule_data.get('rule_type', 'percentage'),
                'markup_value': float(rule_data.get('markup_value', 0)),
                'conditions': rule_data.get('conditions', {}),
                'is_active': rule_data.get('is_active', True),
                'created_at': datetime.now(timezone.utc)
            }
            
            rule_id = mongodb_service.create_pricing_rule(rule_record)
            return rule_id
            
        except Exception as e:
            logger.error(f"Error creating pricing rule: {e}")
            return None
    
    def get_pricing_rules(self, active_only: bool = True) -> List[Dict]:
        """Get pricing rules"""
        try:
            return mongodb_service.get_pricing_rules(active_only)
        except Exception as e:
            logger.error(f"Error getting pricing rules: {e}")
            return []
    
    def apply_pricing_rule(self, rule_id: str, product_ids: List[str] = None) -> Dict:
        """Apply a saved pricing rule to products"""
        try:
            # Get the pricing rule
            rule = mongodb_service.db.pricing_rules.find_one({'_id': mongodb_service.ObjectId(rule_id)})
            if not rule:
                return {
                    'success': False,
                    'error': 'Pricing rule not found'
                }
            
            # If no specific products provided, apply to all eligible products
            if not product_ids:
                # Get products that match rule conditions
                filters = {}
                conditions = rule.get('conditions', {})
                
                if 'min_price' in conditions:
                    filters['original_price'] = {'$gte': conditions['min_price']}
                if 'max_price' in conditions:
                    if 'original_price' not in filters:
                        filters['original_price'] = {}
                    filters['original_price']['$lte'] = conditions['max_price']
                
                if 'categories' in conditions and conditions['categories']:
                    filters['shopify_data.product_type'] = {'$in': conditions['categories']}
                
                # Get matching products
                products = mongodb_service.get_products(filters=filters, limit=1000)
                product_ids = [p['product_id'] for p in products]
            
            # Apply pricing
            return self.apply_pricing_to_products(
                product_ids,
                rule['rule_type'],
                rule['markup_value']
            )
            
        except Exception as e:
            logger.error(f"Error applying pricing rule: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_pricing_analytics(self) -> Dict:
        """Get pricing analytics and insights"""
        try:
            analytics = {
                'total_products_with_pricing': 0,
                'average_markup_percentage': 0,
                'average_profit_margin': 0,
                'price_distribution': {},
                'markup_distribution': {},
                'top_profitable_products': []
            }
            
            # Get products with pricing applied
            products_with_pricing = list(mongodb_service.db.products.find({
                'final_price': {'$gt': 0},
                'original_price': {'$gt': 0}
            }))
            
            analytics['total_products_with_pricing'] = len(products_with_pricing)
            
            if products_with_pricing:
                # Calculate averages
                total_markup_percentage = 0
                total_profit_margin = 0
                
                for product in products_with_pricing:
                    original_price = product.get('original_price', 0)
                    final_price = product.get('final_price', 0)
                    
                    if original_price > 0 and final_price > 0:
                        markup_percentage = ((final_price - original_price) / original_price) * 100
                        profit_margin = ((final_price - original_price) / final_price) * 100
                        
                        total_markup_percentage += markup_percentage
                        total_profit_margin += profit_margin
                
                analytics['average_markup_percentage'] = round(
                    total_markup_percentage / len(products_with_pricing), 2
                )
                analytics['average_profit_margin'] = round(
                    total_profit_margin / len(products_with_pricing), 2
                )
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting pricing analytics: {e}")
            return {}