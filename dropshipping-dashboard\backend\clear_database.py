#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to completely clear the database for fresh start with real data
"""
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.mongodb_service import mongodb_service
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clear_all_data():
    """Clear all data from the database"""
    try:
        logger.info("Starting database cleanup...")

        # Ensure MongoDB connection is established
        if not mongodb_service.connect():
            logger.error("Failed to connect to MongoDB")
            return False

        # Clear products collection
        products_result = mongodb_service.db.products.delete_many({})
        logger.info(f"Deleted {products_result.deleted_count} products")
        
        # Clear orders collection
        orders_result = mongodb_service.db.orders.delete_many({})
        logger.info(f"Deleted {orders_result.deleted_count} orders")
        
        # Clear import batches collection
        batches_result = mongodb_service.db.import_batches.delete_many({})
        logger.info(f"Deleted {batches_result.deleted_count} import batches")
        
        # Clear any other collections that might have test data
        try:
            # Clear AI content cache if it exists
            ai_cache_result = mongodb_service.db.ai_content_cache.delete_many({})
            logger.info(f"Deleted {ai_cache_result.deleted_count} AI cache entries")
        except Exception as e:
            logger.info("No AI cache collection found or error clearing it")
        
        try:
            # Clear image processing cache if it exists
            image_cache_result = mongodb_service.db.image_processing_cache.delete_many({})
            logger.info(f"Deleted {image_cache_result.deleted_count} image cache entries")
        except Exception as e:
            logger.info("No image cache collection found or error clearing it")
        
        logger.info("Database cleanup completed successfully!")
        logger.info("You can now upload your real AliExpress data.")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during database cleanup: {e}")
        return False

if __name__ == "__main__":
    print("🧹 Shop Rave Database Cleanup Tool")
    print("=" * 50)
    
    # Confirm with user
    response = input("Are you sure you want to delete ALL data from the database? (yes/no): ")
    if response.lower() not in ['yes', 'y']:
        print("Operation cancelled.")
        sys.exit(0)
    
    # Perform cleanup
    success = clear_all_data()
    
    if success:
        print("\n✅ Database cleared successfully!")
        print("📁 Ready for real AliExpress data upload")
    else:
        print("\n❌ Database cleanup failed!")
        sys.exit(1)
