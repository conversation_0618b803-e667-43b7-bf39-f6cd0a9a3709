#!/usr/bin/env python3
"""
AliExpress Product Image Downloader

A Python application for downloading product images from AliExpress URLs.
Supports batch downloading, progress tracking, and organized file storage.

Author: AI Assistant
Version: 1.0.0
"""

import os
import re
import sys
import time
import json
import logging
import argparse
import requests
from urllib.parse import urlparse, urljoin
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

# Third-party imports
try:
    from bs4 import BeautifulSoup
    from tqdm import tqdm
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install required packages: pip install -r requirements.txt")
    sys.exit(1)


@dataclass
class ProductInfo:
    """Data class for storing product information"""
    product_id: str
    title: str
    url: str
    image_urls: List[str]
    main_image_url: str = ""


class AliExpressImageDownloader:
    """Main class for downloading AliExpress product images"""
    
    def __init__(self, download_dir: str = "downloads", max_workers: int = 5):
        """
        Initialize the downloader
        
        Args:
            download_dir: Directory to save downloaded images
            max_workers: Maximum number of concurrent download threads
        """
        self.download_dir = Path(download_dir)
        self.max_workers = max_workers
        self.session = requests.Session()
        self.setup_session()
        self.setup_logging()
        
        # Create download directory
        self.download_dir.mkdir(exist_ok=True)
        
    def setup_session(self):
        """Configure the requests session with headers and settings"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('aliexpress_downloader.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def extract_product_id(self, url: str) -> Optional[str]:
        """
        Extract product ID from AliExpress URL
        
        Args:
            url: AliExpress product URL
            
        Returns:
            Product ID if found, None otherwise
        """
        patterns = [
            r'/item/(\d+)\.html',
            r'/item/(\d+)/',
            r'productId=(\d+)',
            r'/(\d+)\.html'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        self.logger.warning(f"Could not extract product ID from URL: {url}")
        return None
        
    def validate_url(self, url: str) -> bool:
        """
        Validate if URL is a valid AliExpress product URL
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        parsed = urlparse(url)
        valid_domains = ['aliexpress.com', 'www.aliexpress.com', 'aliexpress.us']
        
        return any(domain in parsed.netloc for domain in valid_domains)
        
    def fetch_product_page(self, url: str) -> Optional[BeautifulSoup]:
        """
        Fetch and parse the product page
        
        Args:
            url: Product page URL
            
        Returns:
            BeautifulSoup object if successful, None otherwise
        """
        try:
            self.logger.info(f"Fetching product page: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            return soup
            
        except requests.RequestException as e:
            self.logger.error(f"Error fetching product page: {e}")
            return None
            
    def extract_product_info(self, soup: BeautifulSoup, url: str) -> Optional[ProductInfo]:
        """
        Extract product information from the parsed page
        
        Args:
            soup: BeautifulSoup object of the product page
            url: Original product URL
            
        Returns:
            ProductInfo object if successful, None otherwise
        """
        try:
            # Extract product ID
            product_id = self.extract_product_id(url)
            if not product_id:
                return None
                
            # Extract product title
            title_selectors = [
                'h1[data-pl="product-title"]',
                '.product-title-text',
                'h1.product-title',
                '.pdp-product-title',
                'h1'
            ]
            
            title = "Unknown Product"
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break
                    
            # Clean title for filename
            title = re.sub(r'[^\w\s-]', '', title)[:100]
            
            # Extract image URLs
            image_urls = self.extract_image_urls(soup)
            
            if not image_urls:
                self.logger.warning(f"No images found for product {product_id}")
                return None
                
            return ProductInfo(
                product_id=product_id,
                title=title,
                url=url,
                image_urls=image_urls,
                main_image_url=image_urls[0] if image_urls else ""
            )
            
        except Exception as e:
            self.logger.error(f"Error extracting product info: {e}")
            return None
            
    def extract_image_urls(self, soup: BeautifulSoup) -> List[str]:
        """
        Extract all product image URLs from the page

        Args:
            soup: BeautifulSoup object of the product page

        Returns:
            List of image URLs
        """
        image_urls = set()

        # First, try to extract from script tags (most reliable for modern AliExpress)
        script_images = self.extract_images_from_scripts(soup)
        image_urls.update(script_images)

        # Common selectors for AliExpress product images
        selectors = [
            # Modern AliExpress selectors
            '.images-view-item img',
            '.product-image img',
            '.gallery-img img',
            '.magnifier-image img',
            '.product-carousel img',
            '.slider-image img',
            '.main-image img',
            # Generic selectors
            'img[data-src*="alicdn.com"]',
            'img[src*="alicdn.com"]',
            'img[alt*="product"]',
            'img[class*="product"]',
            'img[class*="gallery"]'
        ]

        for selector in selectors:
            images = soup.select(selector)
            for img in images:
                # Try different attributes for image URL
                for attr in ['data-src', 'src', 'data-original', 'data-lazy-src']:
                    img_url = img.get(attr)
                    if img_url and 'alicdn.com' in img_url:
                        # Skip very small images (likely icons/thumbnails)
                        if self.is_small_image(img_url):
                            continue
                        # Clean and normalize URL
                        img_url = self.clean_image_url(img_url)
                        if img_url:
                            image_urls.add(img_url)

        # Filter out duplicate images and sort by likely quality
        filtered_urls = self.filter_and_sort_images(list(image_urls))

        return filtered_urls

    def is_small_image(self, url: str) -> bool:
        """
        Check if image URL indicates a small/thumbnail image

        Args:
            url: Image URL to check

        Returns:
            True if likely a small image
        """
        # Check for size indicators in URL
        small_patterns = [
            r'/\d{1,2}x\d{1,2}\.',  # Very small dimensions like 20x20
            r'_\d{1,2}x\d{1,2}\.',  # Small dimensions with underscore
            r'thumb',
            r'icon',
            r'logo'
        ]

        for pattern in small_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True

        return False

    def filter_and_sort_images(self, urls: List[str]) -> List[str]:
        """
        Filter and sort image URLs by likely quality

        Args:
            urls: List of image URLs

        Returns:
            Filtered and sorted list of URLs
        """
        # Remove duplicates and filter
        unique_urls = list(set(urls))

        # Sort by likely quality (larger images first)
        def quality_score(url):
            score = 0
            # Prefer larger dimensions
            size_match = re.search(r'(\d+)x(\d+)', url)
            if size_match:
                width, height = int(size_match.group(1)), int(size_match.group(2))
                score += width * height

            # Prefer certain formats
            if '.jpg' in url or '.jpeg' in url:
                score += 1000
            elif '.png' in url:
                score += 800
            elif '.webp' in url:
                score += 600

            return score

        return sorted(unique_urls, key=quality_score, reverse=True)
        
    def clean_image_url(self, url: str) -> Optional[str]:
        """
        Clean and normalize image URL

        Args:
            url: Raw image URL

        Returns:
            Cleaned URL or None if invalid
        """
        if not url:
            return None

        # Ensure HTTPS first
        if url.startswith('//'):
            url = 'https:' + url
        elif url.startswith('http://'):
            url = url.replace('http://', 'https://')
        elif not url.startswith('https://'):
            url = 'https://' + url

        # Remove size suffixes and get high quality version
        url = re.sub(r'_\d+x\d+\.(jpg|jpeg|png|webp)', r'.\1', url)
        url = re.sub(r'\.jpg_.*$', '.jpg', url)
        url = re.sub(r'\.png_.*$', '.png', url)
        url = re.sub(r'\.webp_.*$', '.webp', url)

        return url if url.startswith('https://') and 'alicdn.com' in url else None
        
    def extract_images_from_scripts(self, soup: BeautifulSoup) -> List[str]:
        """
        Extract image URLs from JavaScript/JSON data in script tags

        Args:
            soup: BeautifulSoup object

        Returns:
            List of image URLs found in scripts
        """
        image_urls = set()

        # Look for script tags containing image data
        scripts = soup.find_all('script')

        for script in scripts:
            script_content = script.string if script.string else ""

            # Skip if no content
            if not script_content or 'alicdn.com' not in script_content:
                continue

            # Look for various patterns of image URLs in JavaScript
            patterns = [
                # Standard URL patterns
                r'"(https?://[^"]*alicdn\.com[^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
                r"'(https?://[^']*alicdn\.com[^']*\.(jpg|jpeg|png|webp)[^']*)'",
                # URLs without protocol
                r'"(//[^"]*alicdn\.com[^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
                # Image arrays and objects
                r'"imageUrl[^"]*":\s*"([^"]*alicdn\.com[^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
                r'"url[^"]*":\s*"([^"]*alicdn\.com[^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
                r'"src[^"]*":\s*"([^"]*alicdn\.com[^"]*\.(jpg|jpeg|png|webp)[^"]*)"',
                # Product image specific patterns
                r'"productImages[^"]*":\s*\[([^\]]*)\]',
                r'"galleryImages[^"]*":\s*\[([^\]]*)\]'
            ]

            for pattern in patterns:
                matches = re.findall(pattern, script_content, re.IGNORECASE)
                for match in matches:
                    # Handle tuple results from regex groups
                    url = match[0] if isinstance(match, tuple) else match

                    # Skip very small images
                    if self.is_small_image(url):
                        continue

                    cleaned_url = self.clean_image_url(url)
                    if cleaned_url:
                        image_urls.add(cleaned_url)

            # Also look for base64 or data URLs that might contain image references
            # Extract any large image URLs (likely product images)
            large_image_pattern = r'(https?://[^"\s]*alicdn\.com[^"\s]*(?:_\d{3,}x\d{3,}|\.jpg|\.jpeg|\.png|\.webp)[^"\s]*)'
            large_matches = re.findall(large_image_pattern, script_content, re.IGNORECASE)

            for url in large_matches:
                if not self.is_small_image(url):
                    cleaned_url = self.clean_image_url(url)
                    if cleaned_url:
                        image_urls.add(cleaned_url)

        return list(image_urls)

    def download_image(self, url: str, filepath: Path) -> bool:
        """
        Download a single image

        Args:
            url: Image URL to download
            filepath: Local file path to save the image

        Returns:
            True if successful, False otherwise
        """
        try:
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()

            # Create directory if it doesn't exist
            filepath.parent.mkdir(parents=True, exist_ok=True)

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            self.logger.debug(f"Downloaded: {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Error downloading {url}: {e}")
            return False

    def download_product_images(self, product_info: ProductInfo) -> Dict[str, bool]:
        """
        Download all images for a product

        Args:
            product_info: ProductInfo object containing image URLs

        Returns:
            Dictionary mapping image URLs to download success status
        """
        # Create product directory
        safe_title = re.sub(r'[^\w\s-]', '', product_info.title)[:50]
        product_dir = self.download_dir / f"{product_info.product_id}_{safe_title}"
        product_dir.mkdir(exist_ok=True)

        results = {}

        # Download images with progress bar
        with tqdm(total=len(product_info.image_urls), desc=f"Downloading {product_info.product_id}") as pbar:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit download tasks
                future_to_url = {}
                for i, url in enumerate(product_info.image_urls):
                    # Generate filename
                    ext = self.get_file_extension(url)
                    filename = f"image_{i+1:03d}{ext}"
                    filepath = product_dir / filename

                    future = executor.submit(self.download_image, url, filepath)
                    future_to_url[future] = url

                # Process completed downloads
                for future in as_completed(future_to_url):
                    url = future_to_url[future]
                    try:
                        success = future.result()
                        results[url] = success
                    except Exception as e:
                        self.logger.error(f"Download task failed for {url}: {e}")
                        results[url] = False
                    finally:
                        pbar.update(1)

        # Save metadata
        self.save_product_metadata(product_info, product_dir, results)

        successful_downloads = sum(1 for success in results.values() if success)
        self.logger.info(f"Downloaded {successful_downloads}/{len(product_info.image_urls)} images for product {product_info.product_id}")

        return results

    def get_file_extension(self, url: str) -> str:
        """
        Get file extension from URL

        Args:
            url: Image URL

        Returns:
            File extension with dot (e.g., '.jpg')
        """
        parsed = urlparse(url)
        path = parsed.path.lower()

        if path.endswith('.jpg') or path.endswith('.jpeg'):
            return '.jpg'
        elif path.endswith('.png'):
            return '.png'
        elif path.endswith('.webp'):
            return '.webp'
        else:
            return '.jpg'  # Default to jpg

    def save_product_metadata(self, product_info: ProductInfo, product_dir: Path, download_results: Dict[str, bool]):
        """
        Save product metadata to JSON file

        Args:
            product_info: ProductInfo object
            product_dir: Directory where product images are saved
            download_results: Results of image downloads
        """
        metadata = {
            'product_id': product_info.product_id,
            'title': product_info.title,
            'url': product_info.url,
            'download_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_images': len(product_info.image_urls),
            'successful_downloads': sum(1 for success in download_results.values() if success),
            'image_urls': product_info.image_urls,
            'download_results': download_results
        }

        metadata_file = product_dir / 'metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

    def download_from_url(self, url: str) -> bool:
        """
        Download images from a single AliExpress product URL

        Args:
            url: AliExpress product URL

        Returns:
            True if successful, False otherwise
        """
        if not self.validate_url(url):
            self.logger.error(f"Invalid AliExpress URL: {url}")
            return False

        # Fetch and parse product page
        soup = self.fetch_product_page(url)
        if not soup:
            return False

        # Extract product information
        product_info = self.extract_product_info(soup, url)
        if not product_info:
            return False

        # Download images
        results = self.download_product_images(product_info)

        return any(results.values())

    def download_from_urls(self, urls: List[str]) -> Dict[str, bool]:
        """
        Download images from multiple AliExpress product URLs

        Args:
            urls: List of AliExpress product URLs

        Returns:
            Dictionary mapping URLs to download success status
        """
        results = {}

        for url in tqdm(urls, desc="Processing URLs"):
            try:
                success = self.download_from_url(url)
                results[url] = success

                # Add delay between requests to be respectful
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"Error processing URL {url}: {e}")
                results[url] = False

        return results


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Download product images from AliExpress URLs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Download from a single URL
  python aliexpress_image_downloader.py -u "https://www.aliexpress.com/item/1234567890.html"

  # Download from multiple URLs
  python aliexpress_image_downloader.py -u "url1" "url2" "url3"

  # Download from URLs in a file
  python aliexpress_image_downloader.py -f urls.txt

  # Specify custom download directory
  python aliexpress_image_downloader.py -u "url" -d "my_downloads"

  # Use more concurrent workers
  python aliexpress_image_downloader.py -u "url" -w 10
        """
    )

    parser.add_argument(
        '-u', '--urls',
        nargs='+',
        help='AliExpress product URLs to download images from'
    )

    parser.add_argument(
        '-f', '--file',
        help='File containing AliExpress URLs (one per line)'
    )

    parser.add_argument(
        '-d', '--download-dir',
        default='downloads',
        help='Directory to save downloaded images (default: downloads)'
    )

    parser.add_argument(
        '-w', '--workers',
        type=int,
        default=5,
        help='Number of concurrent download workers (default: 5)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='AliExpress Image Downloader 1.0.0'
    )

    return parser


def read_urls_from_file(filepath: str) -> List[str]:
    """
    Read URLs from a text file

    Args:
        filepath: Path to the file containing URLs

    Returns:
        List of URLs
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        return urls
    except FileNotFoundError:
        print(f"Error: File '{filepath}' not found")
        return []
    except Exception as e:
        print(f"Error reading file '{filepath}': {e}")
        return []


def main():
    """Main function"""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Collect URLs
    urls = []

    if args.urls:
        urls.extend(args.urls)

    if args.file:
        file_urls = read_urls_from_file(args.file)
        urls.extend(file_urls)

    if not urls:
        parser.print_help()
        print("\nError: No URLs provided. Use -u for URLs or -f for file input.")
        sys.exit(1)

    # Remove duplicates while preserving order
    seen = set()
    unique_urls = []
    for url in urls:
        if url not in seen:
            seen.add(url)
            unique_urls.append(url)

    print(f"Starting download for {len(unique_urls)} URL(s)...")
    print(f"Download directory: {args.download_dir}")
    print(f"Concurrent workers: {args.workers}")
    print("-" * 50)

    # Initialize downloader
    downloader = AliExpressImageDownloader(
        download_dir=args.download_dir,
        max_workers=args.workers
    )

    # Start downloads
    start_time = time.time()
    results = downloader.download_from_urls(unique_urls)
    end_time = time.time()

    # Print summary
    successful = sum(1 for success in results.values() if success)
    failed = len(results) - successful

    print("-" * 50)
    print(f"Download completed in {end_time - start_time:.2f} seconds")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")

    if failed > 0:
        print("\nFailed URLs:")
        for url, success in results.items():
            if not success:
                print(f"  - {url}")

    print(f"\nImages saved to: {args.download_dir}")


if __name__ == "__main__":
    main()
