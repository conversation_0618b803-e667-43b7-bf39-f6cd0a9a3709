#!/usr/bin/env python3

# Test the path logic
image_file = r"static/images\68855a814a4c7de804a83a1d\3256806060264024_Aliexpress\image_001.jpg"

print("Testing different patterns:")
print(f"Contains 'static/images/': {'static/images/' in image_file}")
print(f"Contains 'static\\images\\': {'static\\images\\' in image_file}")
print(f"Contains 'static/images\\': {'static/images\\' in image_file}")
print(f"Contains 'static\\images/': {'static\\images/' in image_file}")
print()

print(f"Original path: {image_file}")

# Test the new logic
try:
    # Normalize the path first
    normalized_path = image_file.replace('\\', '/')
    print(f"Normalized path: {normalized_path}")

    # Find the static/images part in the normalized path
    static_images_index = normalized_path.find('static/images/')
    print(f"static/images/ index in normalized: {static_images_index}")

    if static_images_index != -1:
        # Get everything after static/images/
        relative_path = normalized_path[static_images_index + len('static/images/'):]
        image_url = f"/api/images/images/{relative_path}"
        print(f"SUCCESS: {image_url}")
    else:
        # Try with mixed separators (static/images\)
        mixed_index = image_file.find('static/images\\')
        print(f"Mixed separator index: {mixed_index}")

        if mixed_index != -1:
            # Get everything after static/images\
            relative_path = image_file[mixed_index + len('static/images\\'):]
            print(f"Relative path after mixed: {relative_path}")
            # Replace backslashes with forward slashes for URL
            relative_path = relative_path.replace('\\', '/')
            print(f"After replacing backslashes: {relative_path}")
            image_url = f"/api/images/images/{relative_path}"
            print(f"SUCCESS (mixed): {image_url}")
        else:
            print("FALLBACK: Using filename only")

except Exception as e:
    print(f"ERROR: {e}")
