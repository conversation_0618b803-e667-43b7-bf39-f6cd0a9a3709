[{"orderId": "TEST001", "orderDate": "2025-01-15", "buyer": {"name": "<PERSON>", "email": "<EMAIL>"}, "items": [{"title": "Wireless Bluetooth Headphones", "price": 29.99, "quantity": 1, "image": "https://example.com/headphones.jpg", "description": "High-quality wireless headphones with noise cancellation"}], "priceData": {"total": 29.99, "shipping": 0.0}, "status": "Completed", "deliveryStatus": "Delivered"}, {"orderId": "TEST002", "orderDate": "2025-01-16", "buyer": {"name": "<PERSON>", "email": "<EMAIL>"}, "items": [{"title": "Smart Phone Case", "price": 15.99, "quantity": 2, "image": "https://example.com/phonecase.jpg", "description": "Protective case for smartphones with kickstand"}], "priceData": {"total": 31.98, "shipping": 2.99}, "status": "Processing", "deliveryStatus": "Shipped"}, {"orderId": "TEST003", "orderDate": "2025-01-17", "buyer": {"name": "<PERSON>", "email": "<EMAIL>"}, "items": [{"title": "USB-C Cable 3ft", "price": 8.99, "quantity": 3, "image": "https://example.com/usbcable.jpg", "description": "Fast charging USB-C cable with data transfer"}], "priceData": {"total": 26.97, "shipping": 1.99}, "status": "Pending", "deliveryStatus": "Processing"}]