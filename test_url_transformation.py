#!/usr/bin/env python3
"""
Test URL transformation logic
"""

def test_url_transformation():
    print("🧪 Testing URL Transformation")
    print("=" * 40)
    
    # Test cases
    test_urls = [
        "//www.aliexpress.com/item/3256808519179538.html",
        "www.aliexpress.com/item/3256808519179538.html",
        "https://www.aliexpress.com/item/3256808519179538.html",
        "http://www.aliexpress.com/item/3256808519179538.html"
    ]
    
    for original_url in test_urls:
        print(f"\nOriginal: {original_url}")
        
        # Apply the same logic as image service
        aliexpress_url = original_url
        if aliexpress_url.startswith('//'):
            # Remove the // and add https://
            aliexpress_url = 'https://' + aliexpress_url[2:]
        elif not aliexpress_url.startswith('http'):
            aliexpress_url = 'https://' + aliexpress_url
            
        print(f"Fixed:    {aliexpress_url}")
        
        # Test if this would work with the downloader
        if aliexpress_url.startswith('https://www.aliexpress.com/'):
            print("✅ Valid format for downloader")
        else:
            print("❌ Invalid format for downloader")

if __name__ == "__main__":
    test_url_transformation()
