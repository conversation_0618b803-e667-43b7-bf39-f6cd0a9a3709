"""
JSON Parser for AliExpress order data
Processes the download.json file structure
"""
import json
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional, Any
from services.mongodb_service import mongodb_service

logger = logging.getLogger(__name__)

class JSONParser:
    """Parser for AliExpress JSON order data"""
    
    def __init__(self):
        self.required_order_fields = [
            'orderId', 'orderDate', 'status', 'buyer', 'items', 'currency'
        ]
        self.required_item_fields = [
            'title', 'priceAmount', 'quantity'
        ]
    
    def validate_structure(self, data: List[Dict]) -> Dict:
        """Validate JSON structure"""
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'sample_order': None,
            'total_items': 0
        }
        
        try:
            if not isinstance(data, list):
                validation_results['is_valid'] = False
                validation_results['errors'].append('Data must be an array of orders')
                return validation_results
            
            if len(data) == 0:
                validation_results['is_valid'] = False
                validation_results['errors'].append('No orders found in data')
                return validation_results
            
            # Validate first order structure
            first_order = data[0]
            validation_results['sample_order'] = first_order
            
            # Check required order fields
            for field in self.required_order_fields:
                if field not in first_order:
                    validation_results['errors'].append(f'Missing required field: {field}')
                    validation_results['is_valid'] = False
            
            # Check items structure
            if 'items' in first_order and isinstance(first_order['items'], list):
                if len(first_order['items']) > 0:
                    first_item = first_order['items'][0]
                    for field in self.required_item_fields:
                        if field not in first_item:
                            validation_results['warnings'].append(f'Missing item field: {field}')
                
                # Count total items across all orders
                for order in data:
                    if 'items' in order and isinstance(order['items'], list):
                        validation_results['total_items'] += len(order['items'])
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating JSON structure: {e}")
            validation_results['is_valid'] = False
            validation_results['errors'].append(f'Validation error: {str(e)}')
            return validation_results
    
    def parse_orders(self, data: List[Dict], batch_id: str) -> Dict:
        """Parse orders from JSON data"""
        results = {
            'success': True,
            'processed_orders': 0,
            'processed_products': 0,
            'errors': [],
            'warnings': []
        }
        
        try:
            for order_data in data:
                try:
                    # Process order
                    order_result = self._process_order(order_data, batch_id)
                    if order_result['success']:
                        results['processed_orders'] += 1
                        results['processed_products'] += order_result['product_count']
                    else:
                        results['errors'].extend(order_result['errors'])
                        
                except Exception as e:
                    error_msg = f"Error processing order {order_data.get('orderId', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            return results
            
        except Exception as e:
            logger.error(f"Error parsing orders: {e}")
            results['success'] = False
            results['errors'].append(f'Parsing error: {str(e)}')
            return results
    
    def _process_order(self, order_data: Dict, batch_id: str) -> Dict:
        """Process a single order"""
        result = {
            'success': True,
            'product_count': 0,
            'errors': []
        }
        
        try:
            # Create order record
            order_record = self._create_order_record(order_data, batch_id)
            order_id = mongodb_service.create_order(order_record)
            
            if not order_id:
                result['success'] = False
                result['errors'].append(f"Failed to create order {order_data.get('orderId')}")
                return result
            
            # Process items as products
            if 'items' in order_data and isinstance(order_data['items'], list):
                for item in order_data['items']:
                    product_result = self._process_product(item, order_data, batch_id)
                    if product_result['success']:
                        result['product_count'] += 1
                    else:
                        result['errors'].extend(product_result['errors'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing order: {e}")
            result['success'] = False
            result['errors'].append(str(e))
            return result
    
    def _create_order_record(self, order_data: Dict, batch_id: str) -> Dict:
        """Create order record for database"""
        return {
            'order_id': order_data.get('orderId'),
            'order_date': order_data.get('orderDate'),
            'order_timestamp': order_data.get('orderDateTimestampFormat'),
            'status': order_data.get('status', 'Unknown'),
            'buyer': order_data.get('buyer', {}),
            'items': order_data.get('items', []),
            'payment': {
                'method': order_data.get('paymentMethod'),
                'currency': order_data.get('currency'),
                'total': order_data.get('priceData', {}).get('total', 0),
                'discount': order_data.get('priceData', {}).get('discount', 0),
                'shipping': order_data.get('priceData', {}).get('shipping', 0),
                'vat': order_data.get('priceData', {}).get('vat', 0)
            },
            'tracking': order_data.get('trackingData', {}),
            'seller': order_data.get('seller', {}),
            'import_batch_id': batch_id,
            'raw_data': order_data  # Store original data for reference
        }
    
    def _process_product(self, item_data: Dict, order_data: Dict, batch_id: str) -> Dict:
        """Process item as product"""
        result = {
            'success': True,
            'errors': []
        }
        
        try:
            # Create unique product ID (order_id + item index or use existing logic)
            product_id = f"{order_data.get('orderId')}_{item_data.get('title', '')[:20]}"
            
            # Check if product already exists
            existing_product = mongodb_service.get_product(product_id)
            if existing_product:
                # Product already exists, skip or update
                return result
            
            # Extract image URLs from item
            image_urls = []
            if 'productImage' in item_data:
                image_urls.append(item_data['productImage'])
            
            # Create product record
            product_record = {
                'product_id': product_id,
                'title': item_data.get('title', 'Untitled Product'),
                'original_price': float(item_data.get('priceAmount', 0)),
                'markup_percentage': 0,  # Default, will be set by pricing rules
                'final_price': float(item_data.get('priceAmount', 0)),
                'currency': order_data.get('currency', 'USD'),
                'status': 'pending',
                'images': [
                    {
                        'url': url,
                        'local_path': '',
                        'processed_path': '',
                        'is_main': i == 0,
                        'is_selected_by_user': i == 0,
                        'background_removal': {
                            'status': 'pending',
                            'api_provider': '',
                            'cost': 0
                        },
                        'download_status': 'pending'
                    }
                    for i, url in enumerate(image_urls)
                ],
                'original_content': {
                    'title': item_data.get('title', ''),
                    'description': item_data.get('title', '')  # Use title as description initially
                },
                'ai_content': {
                    'title': {
                        'original': item_data.get('title', ''),
                        'rewritten': '',
                        'llm_provider': '',
                        'rewrite_status': 'pending',
                        'user_approved': False
                    },
                    'description': {
                        'original': item_data.get('title', ''),
                        'rewritten': '',
                        'llm_provider': '',
                        'rewrite_status': 'pending',
                        'user_approved': False,
                        'seo_keywords': []
                    }
                },
                'aliexpress_data': {
                    'original_url': item_data.get('productLink', ''),
                    'seller': order_data.get('seller', {}),
                    'attributes': item_data.get('attributes', ''),
                    'quantity': item_data.get('quantity', 1)
                },
                'shopify_data': {
                    'prepared': False,
                    'product_type': self._guess_product_type(item_data.get('title', '')),
                    'tags': self._extract_tags(item_data.get('title', '')),
                    'seo_title': '',
                    'seo_description': ''
                },
                'order_info': {
                    'order_id': order_data.get('orderId'),
                    'order_date': order_data.get('orderDate'),
                    'buyer_country': order_data.get('buyer', {}).get('country', '')
                },
                'import_batch_id': batch_id
            }
            
            # Create product in database
            created_id = mongodb_service.create_product(product_record)
            if not created_id:
                result['success'] = False
                result['errors'].append(f"Failed to create product: {product_record['title']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing product: {e}")
            result['success'] = False
            result['errors'].append(str(e))
            return result
    
    def _guess_product_type(self, title: str) -> str:
        """Guess product type from title"""
        title_lower = title.lower()
        
        if any(word in title_lower for word in ['bracelet', 'necklace', 'ring', 'earring', 'jewelry']):
            return 'Jewelry'
        elif any(word in title_lower for word in ['shirt', 'dress', 'pants', 'clothing']):
            return 'Clothing'
        elif any(word in title_lower for word in ['phone', 'case', 'charger', 'electronic']):
            return 'Electronics'
        elif any(word in title_lower for word in ['bag', 'wallet', 'accessory']):
            return 'Accessories'
        else:
            return 'General'
    
    def _extract_tags(self, title: str) -> List[str]:
        """Extract tags from product title"""
        title_lower = title.lower()
        tags = []
        
        # Common keywords to extract as tags
        keywords = [
            'waterproof', 'metal', 'jewelry', 'bracelet', 'necklace', 'ring',
            'women', 'men', 'unisex', 'party', 'casual', 'elegant', 'vintage',
            'modern', 'fashion', 'style', 'accessories', 'gift'
        ]
        
        for keyword in keywords:
            if keyword in title_lower:
                tags.append(keyword)
        
        return tags[:10]  # Limit to 10 tags