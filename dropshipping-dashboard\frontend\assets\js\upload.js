/* Upload JavaScript */

document.addEventListener('DOMContentLoaded', () => {
    const { API_URL, showToast, showLoading, hideLoading } = window.app;

    // DOM Elements
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    const browseFilesBtn = document.getElementById('browse-files');
    const uploadProgress = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressDetails = document.getElementById('progress-details');

    const handleFileUpload = async (file) => {
        if (!file || file.type !== 'application/json') {
            showToast('Please select a valid JSON file', 'error');
            return;
        }

        // Show progress bar immediately
        uploadProgress.classList.remove('hidden');
        uploadProgress.style.display = 'block';
        progressDetails.textContent = 'Uploading file...';
        progressFill.style.width = '10%';

        showLoading();

        const formData = new FormData();
        formData.append('file', file);

        try {
            progressDetails.textContent = 'Processing upload...';
            progressFill.style.width = '30%';

            const response = await fetch(`${API_URL}/upload/json`, {
                method: 'POST',
                body: formData,
            });

            progressFill.style.width = '70%';
            progressDetails.textContent = 'Parsing data...';

            const result = await response.json();

            if (result.success) {
                progressFill.style.width = '80%';
                progressDetails.textContent = 'Processing data...';

                // Start polling for status updates
                const batchId = result.data.batch_id;
                pollUploadStatus(batchId);

            } else {
                showToast(result.error || 'Upload failed', 'error');
                hideLoading();
                uploadProgress.classList.add('hidden');
            }
        } catch (error) {
            console.error('Upload error:', error);
            showToast('Failed to upload file. Please check your connection and try again.', 'error');
        } finally {
            hideLoading();
            setTimeout(() => {
                uploadProgress.classList.add('hidden');
                progressFill.style.width = '0%';
                progressDetails.textContent = '';
            }, 3000);
        }
    };

    const pollUploadStatus = async (batchId) => {
        const maxAttempts = 30; // 30 attempts = 5 minutes max
        let attempts = 0;

        const poll = async () => {
            try {
                attempts++;
                const response = await fetch(`${API_URL}/upload/status/${batchId}`);
                const result = await response.json();

                if (result.success) {
                    const batch = result.data;

                    if (batch.status === 'completed') {
                        progressFill.style.width = '100%';
                        progressDetails.textContent = 'Processing completed successfully!';

                        const ordersCount = batch.processed_orders || 0;
                        const productsCount = batch.processed_products || 0;

                        showToast(`Upload successful! Processed ${ordersCount} orders and ${productsCount} products.`, 'success');

                        // Refresh dashboard stats
                        setTimeout(() => {
                            if (window.app && window.app.refreshStats) {
                                window.app.refreshStats();
                            }
                            hideLoading();
                        }, 1000);

                        return; // Stop polling

                    } else if (batch.status === 'failed') {
                        progressDetails.textContent = 'Processing failed!';
                        showToast(`Upload failed: ${batch.error_message || 'Unknown error'}`, 'error');
                        hideLoading();
                        return; // Stop polling

                    } else if (batch.status === 'processing') {
                        // Update progress based on processed items
                        const processed = (batch.processed_orders || 0) + (batch.processed_products || 0);
                        const total = (batch.total_orders || 0) + (batch.total_products || 0) || 1;
                        const progress = Math.min(80 + (processed / total) * 15, 95); // 80-95% range

                        progressFill.style.width = `${progress}%`;
                        progressDetails.textContent = `Processing... ${batch.processed_orders || 0} orders, ${batch.processed_products || 0} products`;
                    }
                }

                // Continue polling if not completed and under max attempts
                if (attempts < maxAttempts) {
                    setTimeout(poll, 2000); // Poll every 2 seconds
                } else {
                    progressDetails.textContent = 'Processing is taking longer than expected...';
                    showToast('Upload is still processing. Please check back later.', 'warning');
                }

            } catch (error) {
                console.error('Status polling error:', error);
                if (attempts < maxAttempts) {
                    setTimeout(poll, 2000);
                }
            }
        };

        // Start polling after a short delay
        setTimeout(poll, 1000);
    };

    const importJsonBtn = document.querySelector('[data-action="import-json"]') ||
                         document.getElementById('import-json-btn') ||
                         document.querySelector('button[onclick="importData()"]');

    browseFilesBtn.addEventListener('click', () => {
        fileInput.click();
    });

    importJsonBtn.addEventListener('click', () => {
        fileInput.click();
    });

    fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        handleFileUpload(file);
    });

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('active');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('active');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('active');
        const file = e.dataTransfer.files[0];
        handleFileUpload(file);
    });
});