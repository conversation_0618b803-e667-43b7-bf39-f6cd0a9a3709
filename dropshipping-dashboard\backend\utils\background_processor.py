"""
Background processor for handling async tasks
"""
import json
import logging
from datetime import datetime, timezone
from services.mongodb_service import mongodb_service
from utils.json_parser import J<PERSON>NParser

logger = logging.getLogger(__name__)

def process_json_file(batch_id: str, file_path: str):
    """Process uploaded JSON file in background"""
    try:
        logger.info(f"Starting background processing for batch {batch_id}")
        
        # Update batch status
        mongodb_service.update_import_batch(batch_id, {
            'status': 'processing',
            'processing_log': [
                {
                    'timestamp': datetime.now(timezone.utc),
                    'message': 'Started processing JSON file',
                    'level': 'info'
                }
            ]
        })
        
        # Read and parse JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Update batch with total counts
        mongodb_service.update_import_batch(batch_id, {
            'total_orders': len(data),
            'total_products': sum(len(order.get('items', [])) for order in data)
        })
        
        # Process the data
        parser = JSONParser()
        results = parser.parse_orders(data, batch_id)
        
        # Update batch with results
        final_status = 'completed' if results['success'] else 'failed'
        
        update_data = {
            'status': final_status,
            'processed_orders': results['processed_orders'],
            'processed_products': results['processed_products'],
            'processing_log': [
                {
                    'timestamp': datetime.now(timezone.utc),
                    'message': f'Processing completed. Orders: {results["processed_orders"]}, Products: {results["processed_products"]}',
                    'level': 'info'
                }
            ]
        }
        
        # Add errors to log if any
        if results['errors']:
            for error in results['errors']:
                update_data['processing_log'].append({
                    'timestamp': datetime.now(timezone.utc),
                    'message': error,
                    'level': 'error'
                })
        
        mongodb_service.update_import_batch(batch_id, update_data)
        
        logger.info(f"Background processing completed for batch {batch_id}")
        
    except Exception as e:
        error_msg = f"Background processing failed for batch {batch_id}: {str(e)}"
        logger.error(error_msg)
        
        # Update batch with error
        mongodb_service.update_import_batch(batch_id, {
            'status': 'failed',
            'processing_log': [
                {
                    'timestamp': datetime.now(timezone.utc),
                    'message': error_msg,
                    'level': 'error'
                }
            ]
        })

# Simple task queue simulation (in production, use Celery or similar)
class SimpleTaskQueue:
    """Simple task queue for background processing"""
    
    def __init__(self):
        self.tasks = []
    
    def delay(self, func, *args, **kwargs):
        """Add task to queue (simulate Celery's delay)"""
        import threading
        
        def run_task():
            try:
                func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Task failed: {e}")
        
        thread = threading.Thread(target=run_task)
        thread.daemon = True
        thread.start()
        
        return thread

# Create task queue instance
task_queue = SimpleTaskQueue()

# Monkey patch the process_json_file function to work with our simple queue
process_json_file.delay = lambda batch_id, file_path: task_queue.delay(process_json_file, batch_id, file_path)