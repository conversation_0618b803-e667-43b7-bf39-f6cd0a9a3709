<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Management - R.A.V.E</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            background: #ffffff;
            margin: 0;
            padding: 0;
            color: #1f2937;
            line-height: 1.6;
        }

        /* Navigation Header */
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2563eb;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .nav-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .nav-btn.secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .nav-btn:hover {
            background: #2563eb;
            color: white;
        }

        .nav-btn.secondary:hover {
            background: #e2e8f0;
            color: #374151;
        }

        .main-content {
            padding: 40px 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1e293b;
            margin-bottom: 30px;
            text-align: center;
            font-size: 2rem;
        }

        .controls-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e2e8f0;
        }

        .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .search-section {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            width: 200px;
        }

        .bulk-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .bulk-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            width: 100px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .products-table th,
        .products-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .products-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .products-table tbody tr:hover {
            background: #f9fafb;
        }

        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
        }

        .product-title {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .product-price {
            font-weight: 600;
            color: #10b981;
        }

        .product-inventory {
            text-align: center;
        }

        .product-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            border: none;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            color: white;
        }

        .action-btn.edit {
            background: #f59e0b;
        }

        .action-btn.view {
            background: #3b82f6;
        }

        .select-checkbox {
            width: 16px;
            height: 16px;
        }

        .loading-state, .empty-state {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .loading-state i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #10b981;
        }

        .notification.error {
            background: #ef4444;
        }

        .notification.warning {
            background: #f59e0b;
        }

        .notification.info {
            background: #3b82f6;
        }

        .notification-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-left: 15px;
        }

        @media (max-width: 768px) {
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .bulk-controls {
                justify-content: center;
            }

            .products-table {
                font-size: 12px;
            }

            .product-title {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fab fa-shopify"></i> R.A.V.E
            </div>
            <nav class="nav-buttons">
                <a href="index.html" class="nav-btn secondary">Dashboard</a>
                <a href="upload.html" class="nav-btn secondary">Upload Data</a>
                <a href="workspace.html" class="nav-btn secondary">Shopify Workspace</a>
                <a href="shopify-management.html" class="nav-btn primary">Shopify Management</a>
            </nav>
        </div>
    </header>

    <div class="main-content">
        <div class="container">
            <h1><i class="fab fa-shopify"></i> Shopify Store Management</h1>

            <div class="controls-section">
                <div class="controls-row">
                    <div class="search-section">
                        <input type="text" id="search-input" class="search-input" placeholder="Search products...">
                        <button class="btn btn-secondary" id="refresh-btn">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    
                    <div class="bulk-controls">
                        <label>Price Markup %:</label>
                        <input type="number" id="price-markup" class="bulk-input" placeholder="0" min="0" max="1000">
                        
                        <label>Inventory:</label>
                        <input type="number" id="inventory-amount" class="bulk-input" placeholder="100" min="0">
                        
                        <button class="btn btn-success" id="bulk-update-btn">
                            <i class="fas fa-edit"></i> Bulk Update Selected
                        </button>
                    </div>
                </div>

                <div class="controls-row">
                    <div>
                        <button class="btn btn-secondary" id="select-all-btn">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button class="btn btn-secondary" id="clear-selection-btn">
                            <i class="fas fa-square"></i> Clear Selection
                        </button>
                        <span id="selection-count" style="margin-left: 15px; color: #6b7280;"></span>
                    </div>
                </div>
            </div>

            <div id="products-container">
                <table class="products-table" id="products-table">
                    <thead>
                        <tr>
                            <th width="40px">
                                <input type="checkbox" id="select-all-checkbox" class="select-checkbox">
                            </th>
                            <th width="80px">Image</th>
                            <th>Product Title</th>
                            <th width="100px">Price</th>
                            <th width="80px">Inventory</th>
                            <th width="80px">Status</th>
                            <th width="120px">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="products-tbody">
                        <!-- Products will be loaded here -->
                    </tbody>
                </table>

                <div id="loading-state" class="loading-state" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading products from your Shopify store...</p>
                </div>

                <div id="empty-state" class="empty-state" style="display: none;">
                    <i class="fas fa-box-open"></i>
                    <p>No products found in your Shopify store.</p>
                </div>
            </div>
        </div>
    </div>

    <div id="notifications"></div>
    <script src="assets/js/shopify-management.js"></script>
</body>
</html>
