"""
Configuration settings for the Dropshipping Dashboard
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class"""
    
    # Flask Settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    
    # MongoDB Configuration
    MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb+srv://wazobianigeri:<EMAIL>/?retryWrites=true&w=majority')
    MONGODB_DB_NAME = os.getenv('MONGODB_DB_NAME', 'dropshipping_dashboard')
    
    # File Upload Settings
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 100 * 1024 * 1024))  # 100MB
    ALLOWED_EXTENSIONS = {'json'}
    
    # Image Storage Settings
    IMAGE_STORAGE_PATH = os.getenv('IMAGE_STORAGE_PATH', 'static/images')
    PROCESSED_IMAGE_PATH = os.getenv('PROCESSED_IMAGE_PATH', 'static/processed_images')
    
    # AI Service Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    CLAUDE_API_KEY = os.getenv('CLAUDE_API_KEY', '')
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')
    
    # Background Removal Services
    REMOVE_BG_API_KEY = os.getenv('REMOVE_BG_API_KEY', '')
    PHOTOROOM_API_KEY = os.getenv('PHOTOROOM_API_KEY', '')
    CLIPDROP_API_KEY = os.getenv('CLIPDROP_API_KEY', '')
    
    # AI Service Settings
    DEFAULT_LLM_PROVIDER = os.getenv('DEFAULT_LLM_PROVIDER', 'openai')
    DEFAULT_BG_REMOVAL_PROVIDER = os.getenv('DEFAULT_BG_REMOVAL_PROVIDER', 'cloudinary')
    
    # LLM Configuration
    LLM_SETTINGS = {
        'openai': {
            'model': 'gpt-4',
            'temperature': 0.7,
            'max_tokens': 500
        },
        'claude': {
            'model': 'claude-3-sonnet-20240229',
            'temperature': 0.7,
            'max_tokens': 500
        },
        'gemini': {
            'model': 'gemini-pro',
            'temperature': 0.7,
            'max_tokens': 500
        }
    }
    
    # Background Removal Settings
    BG_REMOVAL_SETTINGS = {
        'remove.bg': {
            'size': 'auto',
            'type': 'auto',
            'format': 'png',
            'roi': '0% 0% 100% 100%',
            'crop': False
        },
        'photoroom': {
            'format': 'png',
            'quality': 'high'
        },
        'clipdrop': {
            'format': 'png'
        }
    }
    
    # Cost Tracking
    AI_COST_LIMITS = {
        'daily_limit': float(os.getenv('DAILY_AI_COST_LIMIT', 50.0)),
        'monthly_limit': float(os.getenv('MONTHLY_AI_COST_LIMIT', 500.0))
    }
    
    # Shopify Integration (for future use)
    SHOPIFY_API_KEY = os.getenv('SHOPIFY_API_KEY', '')
    SHOPIFY_API_SECRET = os.getenv('SHOPIFY_API_SECRET', '')
    SHOPIFY_STORE_URL = os.getenv('SHOPIFY_STORE_URL', '')
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS = int(os.getenv('RATE_LIMIT_REQUESTS', 100))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 3600))  # 1 hour
    
    # Async Processing
    MAX_CONCURRENT_DOWNLOADS = int(os.getenv('MAX_CONCURRENT_DOWNLOADS', 5))
    MAX_CONCURRENT_AI_REQUESTS = int(os.getenv('MAX_CONCURRENT_AI_REQUESTS', 3))
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'dropshipping_dashboard.log')
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.IMAGE_STORAGE_PATH, exist_ok=True)
        os.makedirs(Config.PROCESSED_IMAGE_PATH, exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SECRET_KEY = os.getenv('SECRET_KEY')
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # Production-specific initialization
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                cls.LOG_FILE, maxBytes=10240000, backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('Dropshipping Dashboard startup')

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    MONGODB_DB_NAME = 'dropshipping_dashboard_test'

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}