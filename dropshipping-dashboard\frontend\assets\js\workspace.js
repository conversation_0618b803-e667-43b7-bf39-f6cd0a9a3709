/**
 * Shopify Workspace JavaScript
 * Handles displaying selected products with extracted images
 */

document.addEventListener('DOMContentLoaded', async () => {
    // Ensure app is loaded
    const API_URL = window.app?.API_URL || 'http://localhost:5000/api';

    // DOM Elements
    const loading = document.getElementById('loading');
    const emptyState = document.getElementById('empty-state');
    const productsList = document.getElementById('products-list');
    const productsTable = document.getElementById('products-tbody');
    const resetBtn = document.getElementById('reset-statuses-btn');



    // Reset status functionality
    resetBtn.addEventListener('click', async () => {
        if (confirm('Are you sure you want to reset all product statuses? This will clear workspace assignments.')) {
            try {
                showToast('Resetting product statuses...', 'info');
                const response = await fetch(`${API_URL}/workspace/reset-statuses`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showToast(`Reset ${result.data.reset_count} products to imported status`, 'success');
                    setTimeout(() => window.location.href = 'index.html', 1000);
                } else {
                    throw new Error(result.error || 'Failed to reset statuses');
                }
            } catch (error) {
                console.error('Reset error:', error);
                showToast('Failed to reset statuses: ' + error.message, 'error');
            }
        }
    });

    /**
     * Load workspace products
     */
    async function loadWorkspaceProducts() {
        try {
            const response = await fetch(`${API_URL}/workspace/products`);
            const result = await response.json();

            if (loading) loading.style.display = 'none';

            if (result.success && result.data.products && result.data.products.length > 0) {
                await displayProducts(result.data.products);
                if (productsList) productsList.style.display = 'block';
            } else {
                if (emptyState) emptyState.style.display = 'block';
            }
        } catch (error) {
            console.error('Error loading workspace products:', error);
            if (loading) loading.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
            showToast('Failed to load workspace products', 'error');
        }
    }

    /**
     * Display products in table format
     */
    async function displayProducts(products) {
        const rows = [];

        for (const product of products) {
            // Use existing images from the product
            const images = product.images || [];

            // Don't make AI calls during initial load for performance
            const description = product.original_content?.description || product.title || '';
            const price = product.original_price || product.final_price || product.price || 'N/A';
            const title = product.title || 'Untitled Product';

            const imagesHtml = images.length > 0
                ? images.slice(0, 6).map(img => {
                    let imageUrl;

                    // Handle Cloudinary URLs only - no local fallback
                    if (img.cloudinary_url) {
                        imageUrl = img.cloudinary_url;
                    }
                    else if (img.url && img.url.startsWith('http')) {
                        // If it's a full HTTP URL (Cloudinary), use as-is
                        imageUrl = img.url;
                    }
                    else {
                        // No valid Cloudinary URL - skip this image
                        return '';
                    }

                    return `<img src="${imageUrl}" alt="Product" class="product-image"
                                 onerror="this.style.display='none'"
                                 onclick="previewImage('${imageUrl}', '${title}')">`;
                  }).join('')
                : '<span style="color: #9ca3af;">No images extracted</span>';

            rows.push(`
                <tr data-product-id="${product._id}">
                    <td>
                        <div class="product-images">
                            ${imagesHtml}
                        </div>
                        ${images.length > 6 ? `<span class="image-count">+${images.length - 6} more</span>` : ''}
                    </td>
                    <td class="product-title">
                        <div class="title-container">
                            <div class="title-text" id="title-${product._id}">${title}</div>
                            <button class="btn-ai" onclick="enhanceTitle('${product._id}')">✨ Enhance with Gemini</button>
                        </div>
                    </td>
                    <td class="product-description">
                        <div class="description-container">
                            <div class="description-text" id="description-${product._id}">${description}</div>
                            <button class="btn-ai" onclick="enhanceDescription('${product._id}')">✨ Enhance with Gemini</button>
                        </div>
                    </td>
                    <td class="product-price">
                        <div class="price-container">
                            <div class="price-display">$<span id="price-${product._id}">${price}</span></div>
                            <div class="price-controls">
                                <label>Markup %:</label>
                                <input type="number" id="markup-${product._id}" value="50" min="0" max="500"
                                       onchange="updatePrice('${product._id}', ${price})">
                                <button class="btn-small" onclick="applyMarkup('${product._id}', ${price})">Apply</button>
                            </div>
                        </div>
                    </td>
                    <td class="product-actions">
                        <div class="action-buttons-row">
                            <button class="btn-preview" onclick="previewProduct('${product._id}')">👁️ Preview</button>
                            <button class="btn-select-images" onclick="selectImagesForShopify('${product._id}')">🖼️ Select Images</button>
                        </div>
                        <div class="action-buttons-row">
                            <button class="btn-bg-removal" onclick="removeBackgroundFromProduct('${product._id}')">✂️ Remove BG</button>
                            <button class="btn-quick-select" onclick="quickSelectImages('${product._id}')">⚡ Quick Select</button>
                        </div>
                        <div class="action-buttons-row">
                            <button class="btn-export" onclick="exportToShopify('${product._id}')">🚀 Export to Shopify</button>
                        </div>
                    </td>
                </tr>
            `);
        }

        console.log('Generated rows:', rows.length);
        if (productsTable) {
            productsTable.innerHTML = rows.join('');
            console.log('Table populated with', rows.length, 'rows');
        } else {
            console.error('productsTable element not found!');
        }
    }

    // Use global showToast function from app.js
    const showToast = window.app?.showToast || function(message, type) {
        console.log(`${type.toUpperCase()}: ${message}`);
    };

    // Load workspace products and saved state on page load
    loadWorkspaceProducts();
    loadSavedWorkspaceState();

    // Global functions for workspace features
    window.previewImage = function(imageUrl, title) {
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <img src="${imageUrl}" alt="${title}" style="max-width: 90%; max-height: 90%;">
                <p>${title}</p>
            </div>
        `;
        document.body.appendChild(modal);
    };

    window.enhanceTitle = async function(productId) {
        try {
            console.log('Enhancing title for product:', productId);
            const titleElement = document.getElementById(`title-${productId}`);
            if (!titleElement) {
                console.error('Title element not found for product:', productId);
                showToast('Title element not found', 'error');
                return;
            }

            const originalTitle = titleElement.textContent;
            console.log('Original title:', originalTitle);

            // Show custom prompt modal
            const modal = document.createElement('div');
            modal.className = 'prompt-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <h2>✨ Enhance Title with Gemini</h2>
                    <p><strong>Original Title:</strong> ${originalTitle}</p>

                    <div class="prompt-section">
                        <label for="title-prompt">Custom Enhancement Prompt:</label>
                        <textarea id="title-prompt" rows="4" placeholder="Enter your custom prompt for how you want the title enhanced...">AUNTY DIVA style - Create a SHORT product title (under 60 characters) in ALL CAPS format like: AUNTY DIVA CHUNKY HOOPS EARRINGS</textarea>
                    </div>

                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                        <button class="btn-primary" onclick="processEnhancement('${productId}', 'title', '${originalTitle.replace(/'/g, "\\'")}')">✨ Enhance Title</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            console.log('Modal added to body');
        } catch (error) {
            console.error('Error in enhanceTitle:', error);
            showToast('Error opening enhancement modal: ' + error.message, 'error');
        }
    };

    window.enhanceDescription = async function(productId) {
        const descElement = document.getElementById(`description-${productId}`);
        const originalDesc = descElement.textContent;

        // Show custom prompt modal
        const modal = document.createElement('div');
        modal.className = 'prompt-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <h2>✨ Enhance Description with Gemini</h2>
                <p><strong>Original Description:</strong></p>
                <div class="original-content">${originalDesc}</div>

                <div class="prompt-section">
                    <label for="description-prompt">Custom Enhancement Prompt:</label>
                    <textarea id="description-prompt" rows="4" placeholder="Enter your custom prompt for how you want the description enhanced...">Premium product description with specifications, materials, and benefits - Create a detailed AUNTY DIVA style description with luxury language and specifications</textarea>
                </div>

                <div class="modal-actions">
                    <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                    <button class="btn-primary" onclick="processEnhancement('${productId}', 'description', '${originalDesc.replace(/'/g, "\\'")}')">✨ Enhance Description</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    };

    window.processEnhancement = async function(productId, contentType, originalContent) {
        const modal = document.querySelector('.prompt-modal');
        const promptTextarea = document.getElementById(`${contentType}-prompt`);
        const customPrompt = promptTextarea.value.trim();

        if (!customPrompt) {
            showToast('Please enter a custom prompt', 'warning');
            return;
        }

        // Close modal
        modal.remove();

        // Update UI to show processing
        const element = document.getElementById(`${contentType}-${productId}`);
        const originalText = element.textContent || element.innerHTML;
        element.innerHTML = '✨ Enhancing...';

        try {
            const response = await fetch(`${API_URL}/ai/enhance-content`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    content: originalContent,
                    type: contentType,
                    provider: 'gemini',
                    style: customPrompt
                })
            });

            const result = await response.json();
            console.log(`${contentType} enhancement result:`, result);

            if (result.success && result.data && result.data.enhanced_content) {
                // Parse and clean the AI response
                const cleanedOptions = parseAIResponse(result.data.enhanced_content);

                if (cleanedOptions.length > 1) {
                    // Show option selection modal
                    showOptionSelectionModal(productId, contentType, cleanedOptions, originalContent);
                } else {
                    // Single option, use directly
                    const finalContent = cleanedOptions[0] || result.data.enhanced_content;
                    await applyEnhancedContent(productId, contentType, finalContent, element);
                }
            } else {
                console.error('Enhancement failed:', result);
                throw new Error(result.error || 'Enhancement failed');
            }
        } catch (error) {
            // Restore original content on error
            if (contentType === 'description') {
                element.innerHTML = originalText;
            } else {
                element.textContent = originalContent;
            }
            showToast(`Failed to enhance ${contentType}: ${error.message}`, 'error');
        }
    };

    window.updatePrice = function(productId, originalPrice) {
        const markupInput = document.getElementById(`markup-${productId}`);
        const priceDisplay = document.getElementById(`price-${productId}`);
        const markup = parseFloat(markupInput.value) || 0;
        const newPrice = (parseFloat(originalPrice) * (1 + markup / 100)).toFixed(2);
        priceDisplay.textContent = newPrice;

        // Save price change to state
        if (!window.priceChanges) window.priceChanges = {};
        window.priceChanges[productId] = {
            originalPrice: parseFloat(originalPrice),
            markup: markup,
            finalPrice: parseFloat(newPrice)
        };
    };

    window.applyMarkup = function(productId, originalPrice) {
        updatePrice(productId, originalPrice);
        showToast('Markup applied successfully!', 'success');
        saveWorkspaceState(); // Auto-save when markup is applied
    };

    window.previewProduct = async function(productId) {
        try {
            // Get product data from API
            const response = await fetch(`${API_URL}/products/${productId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to get product data');
            }

            const product = result.data;
            const title = product.title || 'Untitled Product';
            const description = product.description || 'No description available';
            const price = product.final_price || product.original_price || 0;

            // Get selected images or all images if none selected
            const imageSelection = window.imageSelections && window.imageSelections[productId];
            let imagesToShow = product.images || [];

            if (imageSelection && imageSelection.selectedImages.length > 0) {
                // Show only selected images
                imagesToShow = imageSelection.selectedImages.map(index => product.images[index]).filter(img => img);

                // Reorder so main image is first
                if (imageSelection.mainImageIndex >= 0 && imageSelection.mainImageIndex < imagesToShow.length) {
                    const mainImage = imagesToShow[imageSelection.mainImageIndex];
                    imagesToShow.splice(imageSelection.mainImageIndex, 1);
                    imagesToShow.unshift(mainImage);
                }
            }

            const imageUrls = imagesToShow.map(img => img.cloudinary_url || img.url).filter(url => url);

            const modal = document.createElement('div');
            modal.className = 'preview-modal';
            modal.innerHTML = `
                <div class="modal-content large">
                    <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <h2>Shopify Export Preview</h2>
                    ${imageSelection ? '<p class="preview-note">Showing selected images for Shopify export</p>' : '<p class="preview-note">No images selected - showing all available images</p>'}
                    <div class="preview-content">
                        <div class="preview-images">
                            ${imageUrls.length > 0
                                ? imageUrls.map((url, index) => `
                                    <div class="preview-image-container">
                                        <img src="${url}" alt="Product Image ${index + 1}">
                                        ${index === 0 && imageSelection ? '<span class="main-image-badge">Main Image</span>' : ''}
                                    </div>
                                `).join('')
                                : '<p>No images available</p>'
                            }
                        </div>
                        <div class="preview-details">
                            <h3>${title}</h3>
                            <div class="preview-description">${description}</div>
                            <div class="preview-price">$${price}</div>
                            ${imageSelection
                                ? `<div class="preview-stats">
                                     <p><strong>Selected Images:</strong> ${imageSelection.selectedImages.length}</p>
                                     <p><strong>Main Image:</strong> Image ${imageSelection.selectedImages[imageSelection.mainImageIndex] + 1}</p>
                                   </div>`
                                : '<div class="preview-warning">⚠️ Please select images before exporting to Shopify</div>'
                            }
                        </div>
                    </div>
                    <div class="modal-actions">
                        ${!imageSelection
                            ? '<button class="btn-secondary" onclick="selectImagesForShopify(\'' + productId + '\'); this.parentElement.parentElement.parentElement.remove()">Select Images First</button>'
                            : ''
                        }
                        <button class="btn-export" onclick="exportToShopify('${productId}')">🚀 Export to Shopify</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

        } catch (error) {
            console.error('Error previewing product:', error);
            showToast('Failed to load product preview: ' + error.message, 'error');
        }
    };

    window.selectImagesForShopify = async function(productId, presetSelection = null) {
        try {
            // Get product data
            const response = await fetch(`${API_URL}/products/${productId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to get product data');
            }

            const product = result.data;
            const images = product.images || [];

            if (images.length === 0) {
                showToast('No images available for this product', 'warning');
                return;
            }

            // Create image selection modal
            const modal = document.createElement('div');
            modal.className = 'image-selection-modal';
            modal.innerHTML = `
                <div class="modal-content large">
                    <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <h2>Select Images for Shopify Export</h2>
                    <p>Choose which images to export and select the main image:</p>

                    <div class="image-selection-grid">
                        ${images.map((img, index) => `
                            <div class="image-selection-item" data-index="${index}">
                                <div class="image-container">
                                    <img src="${img.cloudinary_url || img.url}" alt="Product Image ${index + 1}"
                                         onerror="this.style.display='none'">
                                    <div class="image-overlay">
                                        <label class="checkbox-container">
                                            <input type="checkbox" class="image-checkbox" data-index="${index}" ${index === 0 ? 'checked' : ''}>
                                            <span class="checkmark"></span>
                                        </label>
                                        <label class="radio-container">
                                            <input type="radio" name="main-image-${productId}" class="main-image-radio" data-index="${index}" ${index === 0 ? 'checked' : ''}>
                                            <span class="radio-label">Main</span>
                                        </label>
                                        <button class="btn-remove-bg-single" onclick="removeBackgroundSingle('${productId}', ${index})" title="Remove background from this image">
                                            ✂️
                                        </button>
                                        <button class="btn-undo-bg" onclick="undoBackgroundRemoval('${productId}', ${index})" title="Undo background removal" style="display: none;">
                                            ↶
                                        </button>
                                    </div>
                                </div>
                                <div class="image-info">
                                    Image ${index + 1}
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                        <button class="btn-primary" onclick="confirmImageSelection('${productId}')">Confirm Selection</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Apply preset selection if specified
            if (presetSelection) {
                setTimeout(() => {
                    applyPresetSelection(presetSelection, images.length);
                }, 100);
            }

        } catch (error) {
            console.error('Error selecting images:', error);
            showToast('Failed to load images: ' + error.message, 'error');
        }
    };

    window.confirmImageSelection = function(productId) {
        try {
            const modal = document.querySelector('.image-selection-modal');
            const selectedImages = [];
            let mainImageIndex = 0;

            // Get selected images
            const checkboxes = modal.querySelectorAll('.image-checkbox:checked');
            checkboxes.forEach(checkbox => {
                selectedImages.push(parseInt(checkbox.dataset.index));
            });

            // Get main image
            const mainRadio = modal.querySelector('.main-image-radio:checked');
            if (mainRadio) {
                mainImageIndex = parseInt(mainRadio.dataset.index);
            }

            if (selectedImages.length === 0) {
                showToast('Please select at least one image', 'warning');
                return;
            }

            if (!selectedImages.includes(mainImageIndex)) {
                showToast('Main image must be one of the selected images', 'warning');
                return;
            }

            // Store selection in product data for export
            window.imageSelections = window.imageSelections || {};
            window.imageSelections[productId] = {
                selectedImages: selectedImages,
                mainImageIndex: selectedImages.indexOf(mainImageIndex) // Index within selected images
            };

            console.log('Image selection saved:', productId, window.imageSelections[productId]);

            modal.remove();
            showToast(`Selected ${selectedImages.length} images for export`, 'success');

            // Update visual indicator and save state
            updateProductSelectionIndicator(productId, window.imageSelections[productId]);
            saveWorkspaceState();

            console.log('Selection indicator updated and state saved');

        } catch (error) {
            console.error('Error confirming selection:', error);
            showToast('Failed to confirm selection: ' + error.message, 'error');
        }
    };

    window.exportToShopify = async function(productId) {
        try {
            // Check if images are selected
            const imageSelection = window.imageSelections && window.imageSelections[productId];

            if (!imageSelection) {
                showToast('Please select images first using the "Select Images" button', 'warning');
                return;
            }

            showToast('Exporting to Shopify...', 'info');

            const response = await fetch(`${API_URL}/shopify/export-product/${productId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    selected_images: imageSelection.selectedImages,
                    main_image_index: imageSelection.mainImageIndex
                })
            });

            const result = await response.json();

            if (result.success) {
                showToast('Product exported to Shopify successfully!', 'success');
                // Refresh the workspace to show updated status
                setTimeout(() => location.reload(), 1000);
            } else {
                throw new Error(result.error || 'Failed to export to Shopify');
            }

        } catch (error) {
            console.error('Export error:', error);
            showToast('Failed to export to Shopify: ' + error.message, 'error');
        }
    };



    window.removeBackgroundSingle = async function(productId, imageIndex) {
        try {
            if (confirm('Remove background from this image using Cloudinary AI?')) {
                showToast('Processing image...', 'info');

                // Store original image for undo
                const modal = document.querySelector('.image-selection-modal');
                const imageElement = modal.querySelector(`[data-index="${imageIndex}"] img`);
                const originalSrc = imageElement.src;

                // Store undo data
                if (!window.backgroundRemovalHistory) window.backgroundRemovalHistory = {};
                if (!window.backgroundRemovalHistory[productId]) window.backgroundRemovalHistory[productId] = {};
                window.backgroundRemovalHistory[productId][imageIndex] = {
                    originalUrl: originalSrc,
                    hasBackgroundRemoved: false
                };

                const response = await fetch(`${API_URL}/images/remove-background/${productId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        provider: 'cloudinary',
                        image_indices: [imageIndex]
                    })
                });

                const result = await response.json();

                if (result.success && result.data && result.data.results && result.data.results.length > 0) {
                    const processedResult = result.data.results[0];
                    if (processedResult.success && processedResult.processed_cloudinary_url) {
                        // Add cache-busting parameter to ensure image reloads
                        const processedUrlWithCache = processedResult.processed_cloudinary_url + '?t=' + Date.now();

                        // Replace the image in the modal
                        imageElement.src = processedUrlWithCache;

                        // Force image reload
                        imageElement.onload = function() {
                            console.log('Processed image loaded successfully');
                        };

                        // Update undo data
                        window.backgroundRemovalHistory[productId][imageIndex] = {
                            originalUrl: originalSrc,
                            processedUrl: processedResult.processed_cloudinary_url,
                            hasBackgroundRemoved: true
                        };

                        // Show undo button and hide remove button
                        const imageContainer = modal.querySelector(`[data-index="${imageIndex}"]`);
                        const removeBtn = imageContainer?.querySelector('.btn-remove-bg-single');
                        const undoBtn = imageContainer?.querySelector('.btn-undo-bg');

                        if (removeBtn) {
                            removeBtn.style.display = 'none';
                        }
                        if (undoBtn) {
                            undoBtn.style.display = 'inline-block';
                            undoBtn.style.visibility = 'visible';
                        }

                        // Update the main workspace table image as well
                        updateWorkspaceProductImage(productId, imageIndex, processedResult.processed_cloudinary_url);

                        // Also update the product data in memory to reflect the change
                        updateProductImageInMemory(productId, imageIndex, processedResult.processed_cloudinary_url);

                        showToast('Background removed successfully!', 'success');
                    } else {
                        throw new Error(processedResult.error || 'Failed to process image');
                    }
                } else {
                    throw new Error(result.error || 'Failed to remove background');
                }
            }

        } catch (error) {
            console.error('Single background removal error:', error);
            showToast('Failed to remove background: ' + error.message, 'error');
        }
    };

    window.undoBackgroundRemoval = function(productId, imageIndex) {
        try {
            const history = window.backgroundRemovalHistory?.[productId]?.[imageIndex];
            if (!history || !history.hasBackgroundRemoved) {
                showToast('No background removal to undo', 'warning');
                return;
            }

            // Restore original image
            const modal = document.querySelector('.image-selection-modal');
            const imageElement = modal.querySelector(`[data-index="${imageIndex}"] img`);
            imageElement.src = history.originalUrl;

            // Update history
            window.backgroundRemovalHistory[productId][imageIndex].hasBackgroundRemoved = false;

            // Show remove button and hide undo button
            const removeBtn = modal.querySelector(`[data-index="${imageIndex}"] .btn-remove-bg-single`);
            const undoBtn = modal.querySelector(`[data-index="${imageIndex}"] .btn-undo-bg`);
            if (removeBtn) removeBtn.style.display = 'inline-block';
            if (undoBtn) undoBtn.style.display = 'none';

            // Update the main workspace table image as well
            updateWorkspaceProductImage(productId, imageIndex, history.originalUrl);

            showToast('Background removal undone', 'success');

        } catch (error) {
            console.error('Undo background removal error:', error);
            showToast('Failed to undo background removal: ' + error.message, 'error');
        }
    };

    function updateWorkspaceProductImage(productId, imageIndex, newImageUrl) {
        try {
            // Find the product row in the workspace table
            const productRow = document.querySelector(`tr[data-product-id="${productId}"]`);
            if (!productRow) {
                console.log('Product row not found for:', productId);
                return;
            }

            // Find all product images in that row
            const productImages = productRow.querySelectorAll('.product-image');
            console.log('Found product images:', productImages.length, 'updating index:', imageIndex);

            // Update the specific image if it exists
            if (productImages[imageIndex]) {
                // Add cache-busting parameter
                const urlWithCache = newImageUrl + (newImageUrl.includes('?') ? '&' : '?') + 't=' + Date.now();
                productImages[imageIndex].src = urlWithCache;

                // Add a visual indicator that this image was processed
                productImages[imageIndex].style.border = '3px solid #10b981';
                productImages[imageIndex].style.borderRadius = '4px';
                productImages[imageIndex].title = 'Background removed';

                // Force image reload
                productImages[imageIndex].onload = function() {
                    console.log('Workspace image updated successfully');
                };

                // Remove the border after 3 seconds
                setTimeout(() => {
                    if (productImages[imageIndex]) {
                        productImages[imageIndex].style.border = '';
                        productImages[imageIndex].style.borderRadius = '';
                    }
                }, 3000);

                console.log('Updated workspace image:', imageIndex, 'with URL:', urlWithCache);
            } else {
                console.log('Image at index', imageIndex, 'not found in workspace');
            }

        } catch (error) {
            console.error('Error updating workspace product image:', error);
        }
    }

    function updateProductImageInMemory(productId, imageIndex, newImageUrl) {
        try {
            // This function would update any cached product data
            // For now, we'll just log the update
            console.log('Updating product image in memory:', productId, imageIndex, newImageUrl);

            // If we have any cached product data, update it here
            if (window.cachedProducts && window.cachedProducts[productId]) {
                if (window.cachedProducts[productId].images && window.cachedProducts[productId].images[imageIndex]) {
                    window.cachedProducts[productId].images[imageIndex].cloudinary_url = newImageUrl;
                    window.cachedProducts[productId].images[imageIndex].url = newImageUrl;
                }
            }

        } catch (error) {
            console.error('Error updating product image in memory:', error);
        }
    }

    // New functions for product listing actions
    window.removeBackgroundFromProduct = async function(productId) {
        try {
            showToast('Starting background removal for all product images...', 'info');

            const response = await fetch(`${API_URL}/images/remove-background/${productId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider: 'cloudinary',
                    image_indices: 'all' // Process all images
                })
            });

            const result = await response.json();

            if (result.success) {
                showToast(`Background removed from ${result.data.processed_count} images!`, 'success');

                // Refresh the product row to show updated images
                await refreshProductRow(productId);
            } else {
                throw new Error(result.error || 'Failed to remove backgrounds');
            }

        } catch (error) {
            console.error('Error removing backgrounds:', error);
            showToast('Failed to remove backgrounds: ' + error.message, 'error');
        }
    };

    window.quickSelectImages = async function(productId) {
        try {
            // Get product data
            const response = await fetch(`${API_URL}/products/${productId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to get product data');
            }

            const product = result.data;
            const images = product.images || [];

            if (images.length === 0) {
                showToast('No images found for this product', 'warning');
                return;
            }

            // Show quick selection modal
            const modal = document.createElement('div');
            modal.className = 'quick-select-modal';
            modal.innerHTML = `
                <div class="modal-content large">
                    <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <h2>⚡ Quick Select Images</h2>
                    <p>Select images for Shopify export:</p>

                    <div class="quick-select-options">
                        <button class="btn-primary" onclick="selectAllImages('${productId}')">Select All Images</button>
                        <button class="btn-secondary" onclick="selectFirstFive('${productId}')">Select First 5</button>
                        <button class="btn-secondary" onclick="selectMainImage('${productId}')">Select Main Image Only</button>
                    </div>

                    <div class="images-preview">
                        ${images.slice(0, 10).map((img, index) => {
                            const imageUrl = img.cloudinary_url || img.url;
                            return `
                                <div class="quick-image-item">
                                    <img src="${imageUrl}" alt="Product Image ${index + 1}">
                                    <span>Image ${index + 1}</span>
                                </div>
                            `;
                        }).join('')}
                        ${images.length > 10 ? `<div class="more-images">+${images.length - 10} more images</div>` : ''}
                    </div>

                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                        <button class="btn-primary" onclick="openFullImageSelection('${productId}')">Open Full Selection</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

        } catch (error) {
            console.error('Error in quick select:', error);
            showToast('Failed to open quick select: ' + error.message, 'error');
        }
    };

    window.selectAllImages = function(productId) {
        // Close modal and open full selection with all images selected
        document.querySelector('.quick-select-modal').remove();
        selectImagesForShopify(productId, 'all');
    };

    window.selectFirstFive = function(productId) {
        // Close modal and open full selection with first 5 selected
        document.querySelector('.quick-select-modal').remove();
        selectImagesForShopify(productId, 'first5');
    };

    window.selectMainImage = function(productId) {
        // Close modal and open full selection with main image selected
        document.querySelector('.quick-select-modal').remove();
        selectImagesForShopify(productId, 'main');
    };

    window.openFullImageSelection = function(productId) {
        // Close modal and open full selection
        document.querySelector('.quick-select-modal').remove();
        selectImagesForShopify(productId);
    };

    async function refreshProductRow(productId) {
        try {
            // Get updated product data
            const response = await fetch(`${API_URL}/products/${productId}`);
            const result = await response.json();

            if (result.success) {
                const product = result.data;
                const productRow = document.querySelector(`tr[data-product-id="${productId}"]`);

                if (productRow) {
                    // Update the images in the row
                    const imagesContainer = productRow.querySelector('.product-images');
                    const images = product.images || [];

                    const imagesHtml = images.length > 0
                        ? images.slice(0, 6).map(img => {
                            const imageUrl = img.cloudinary_url || img.url;
                            if (imageUrl) {
                                return `<img src="${imageUrl}?t=${Date.now()}" alt="Product" class="product-image"
                                             onerror="this.style.display='none'"
                                             onclick="previewImage('${imageUrl}', '${product.title}')">`;
                            }
                            return '';
                          }).join('')
                        : '<span style="color: #9ca3af;">No images extracted</span>';

                    imagesContainer.innerHTML = imagesHtml;
                }
            }
        } catch (error) {
            console.error('Error refreshing product row:', error);
        }
    }

    function applyPresetSelection(presetType, totalImages) {
        const checkboxes = document.querySelectorAll('.image-checkbox');
        const radios = document.querySelectorAll('.main-image-radio');

        // First, uncheck all
        checkboxes.forEach(cb => cb.checked = false);
        radios.forEach(radio => radio.checked = false);

        switch (presetType) {
            case 'all':
                checkboxes.forEach(cb => cb.checked = true);
                if (radios[0]) radios[0].checked = true;
                break;

            case 'first5':
                for (let i = 0; i < Math.min(5, checkboxes.length); i++) {
                    checkboxes[i].checked = true;
                }
                if (radios[0]) radios[0].checked = true;
                break;

            case 'main':
                if (checkboxes[0]) checkboxes[0].checked = true;
                if (radios[0]) radios[0].checked = true;
                break;
        }
    }

    async function saveEnhancedContent(productId, contentType, enhancedContent) {
        try {
            const response = await fetch(`${API_URL}/products/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    [contentType]: enhancedContent,
                    [`ai_enhanced_${contentType}`]: true,
                    updated_at: new Date().toISOString()
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to save enhanced content');
            }

            console.log(`Enhanced ${contentType} saved successfully`);

        } catch (error) {
            console.error(`Error saving enhanced ${contentType}:`, error);
            showToast(`Failed to save enhanced ${contentType}: ${error.message}`, 'error');
        }
    }

    function parseAIResponse(response) {
        // Clean up the response and extract options
        let cleanedResponse = response
            .replace(/\*\*/g, '') // Remove ** formatting
            .replace(/\*/g, '') // Remove * formatting
            .trim();

        // Look for numbered options or bullet points
        const optionPatterns = [
            /Option \d+[^:]*:\s*(.+?)(?=Option \d+|$)/gi,
            /\d+\.\s*(.+?)(?=\d+\.|$)/gi,
            /•\s*(.+?)(?=•|$)/gi,
            /-\s*(.+?)(?=-|$)/gi
        ];

        let options = [];

        for (const pattern of optionPatterns) {
            const matches = [...cleanedResponse.matchAll(pattern)];
            if (matches.length > 1) {
                options = matches.map(match => match[1].trim().replace(/\n.*$/s, '').trim());
                break;
            }
        }

        // If no clear options found, try to split by common separators
        if (options.length === 0) {
            const lines = cleanedResponse.split('\n').filter(line => line.trim());
            if (lines.length > 1 && lines.length <= 10) {
                options = lines.map(line => line.trim().replace(/^[•\-\*]\s*/, ''));
            }
        }

        // If still no options, return the cleaned response as single option
        if (options.length === 0) {
            options = [cleanedResponse];
        }

        // Clean each option
        return options.map(option =>
            option
                .replace(/^[•\-\*\d\.]+\s*/, '') // Remove bullets and numbers
                .replace(/\([^)]*\)/g, '') // Remove parenthetical notes
                .trim()
        ).filter(option => option.length > 0);
    }

    function showOptionSelectionModal(productId, contentType, options, originalContent) {
        const modal = document.createElement('div');
        modal.className = 'option-selection-modal';
        modal.innerHTML = `
            <div class="modal-content large">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <h2>✨ Select Your Preferred ${contentType.charAt(0).toUpperCase() + contentType.slice(1)}</h2>
                <p>Choose the option you like best, or edit any option before applying:</p>

                <div class="options-container">
                    ${options.map((option, index) => `
                        <div class="option-item" data-index="${index}">
                            <div class="option-header">
                                <input type="radio" name="content-option" value="${index}" id="option-${index}" ${index === 0 ? 'checked' : ''}>
                                <label for="option-${index}">Option ${index + 1}</label>
                            </div>
                            <textarea class="option-text" data-index="${index}">${option}</textarea>
                        </div>
                    `).join('')}
                </div>

                <div class="modal-actions">
                    <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Cancel</button>
                    <button class="btn-primary" onclick="applySelectedOption('${productId}', '${contentType}')">Apply Selected Option</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    window.applySelectedOption = async function(productId, contentType) {
        const modal = document.querySelector('.option-selection-modal');
        const selectedRadio = modal.querySelector('input[name="content-option"]:checked');
        const selectedIndex = selectedRadio.value;
        const selectedTextarea = modal.querySelector(`textarea[data-index="${selectedIndex}"]`);
        const finalContent = selectedTextarea.value.trim();

        if (!finalContent) {
            showToast('Please enter some content', 'warning');
            return;
        }

        modal.remove();

        const element = document.getElementById(`${contentType}-${productId}`);
        await applyEnhancedContent(productId, contentType, finalContent, element);
    };

    async function applyEnhancedContent(productId, contentType, content, element) {
        try {
            // Update UI
            if (contentType === 'description') {
                element.innerHTML = content.replace(/\n/g, '<br>');
            } else {
                element.textContent = content;
            }

            // Save the enhanced content to the database
            await saveEnhancedContent(productId, contentType, content);

            showToast(`${contentType.charAt(0).toUpperCase() + contentType.slice(1)} enhanced and saved successfully!`, 'success');

        } catch (error) {
            console.error(`Error applying enhanced ${contentType}:`, error);
            showToast(`Failed to apply enhanced ${contentType}: ${error.message}`, 'error');
        }
    }

    // Workspace state management
    function saveWorkspaceState() {
        try {
            const workspaceState = {
                imageSelections: window.imageSelections || {},
                priceChanges: window.priceChanges || {},
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('workspaceState', JSON.stringify(workspaceState));
            showToast('Workspace state saved', 'success');

        } catch (error) {
            console.error('Error saving workspace state:', error);
            showToast('Failed to save workspace state', 'error');
        }
    }

    function loadSavedWorkspaceState() {
        try {
            const savedState = localStorage.getItem('workspaceState');
            console.log('Loading saved state:', savedState);

            if (savedState) {
                const workspaceState = JSON.parse(savedState);
                console.log('Parsed workspace state:', workspaceState);

                // Restore image selections
                window.imageSelections = workspaceState.imageSelections || {};

                // Restore price changes
                window.priceChanges = workspaceState.priceChanges || {};

                // Wait for DOM to be ready before applying changes
                setTimeout(() => {
                    // Apply saved price changes to the UI
                    Object.keys(window.priceChanges).forEach(productId => {
                        const priceElement = document.getElementById(`price-${productId}`);
                        const markupElement = document.getElementById(`markup-${productId}`);

                        if (priceElement && window.priceChanges[productId]) {
                            priceElement.textContent = window.priceChanges[productId].finalPrice;
                            console.log(`Applied price for ${productId}:`, window.priceChanges[productId].finalPrice);
                        }

                        if (markupElement && window.priceChanges[productId]) {
                            markupElement.value = window.priceChanges[productId].markup;
                            console.log(`Applied markup for ${productId}:`, window.priceChanges[productId].markup);
                        }
                    });

                    // Show indicators for products with selections
                    Object.keys(window.imageSelections).forEach(productId => {
                        const selection = window.imageSelections[productId];
                        if (selection && selection.selectedImages.length > 0) {
                            updateProductSelectionIndicator(productId, selection);
                            console.log(`Applied selection indicator for ${productId}:`, selection);
                        }
                    });

                    if (Object.keys(window.imageSelections).length > 0 || Object.keys(window.priceChanges).length > 0) {
                        showToast('Workspace state restored successfully', 'success');
                    }
                }, 1000); // Wait 1 second for products to load

                console.log('Workspace state loaded:', workspaceState);
            } else {
                console.log('No saved workspace state found');
            }
        } catch (error) {
            console.error('Error loading workspace state:', error);
            showToast('Failed to load saved workspace state', 'error');
        }
    }

    function updateProductSelectionIndicator(productId, selection) {
        console.log('Updating selection indicator for:', productId, selection);
        const row = document.querySelector(`tr[data-product-id="${productId}"]`);
        console.log('Found row:', !!row);

        if (row) {
            // Add visual indicator that images are selected
            const selectButton = row.querySelector('.btn-select-images');
            console.log('Found select button:', !!selectButton);

            if (selectButton) {
                const newText = `🖼️ ${selection.selectedImages.length} Selected`;
                selectButton.textContent = newText;
                selectButton.style.background = 'var(--emerald-500)';
                selectButton.style.color = 'white';
                console.log('Updated button text to:', newText);
            }
        }
    }

    function clearWorkspaceState() {
        try {
            localStorage.removeItem('workspaceState');
            window.imageSelections = {};
            window.priceChanges = {};
            showToast('Workspace state cleared', 'info');
            location.reload();
        } catch (error) {
            console.error('Error clearing workspace state:', error);
            showToast('Failed to clear workspace state', 'error');
        }
    }

    // Auto-save when changes are made
    window.addEventListener('beforeunload', function() {
        if (Object.keys(window.imageSelections || {}).length > 0 ||
            Object.keys(window.priceChanges || {}).length > 0) {
            saveWorkspaceState();
        }
    });

    // Global save function
    window.saveWorkspace = saveWorkspaceState;
    window.clearWorkspace = clearWorkspaceState;
});
