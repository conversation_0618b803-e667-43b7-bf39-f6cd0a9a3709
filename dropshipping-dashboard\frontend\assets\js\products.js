/* Products JavaScript */

document.addEventListener('DOMContentLoaded', () => {
    const { API_URL, showToast, showLoading, hideLoading } = window.app;

    // DOM Elements
    const productsGrid = document.getElementById('products-grid');
    const productsPagination = document.getElementById('products-pagination');
    const statusFilter = document.getElementById('status-filter');
    const productSearch = document.getElementById('product-search');

    let currentPage = 1;
    let totalPages = 1;

    const fetchProducts = async (page = 1) => {
        showLoading();
        try {
            const status = statusFilter.value;
            const search = productSearch.value;
            const response = await fetch(`${API_URL}/products?page=${page}&status=${status}&search=${search}`);
            const result = await response.json();

            if (result.success) {
                renderProducts(result.data.products);
                renderPagination(result.data.pagination);
            } else {
                showToast(result.error, 'error');
            }
        } catch (error) {
            showToast('Failed to fetch products', 'error');
        } finally {
            hideLoading();
        }
    };

    const renderProducts = (products) => {
        productsGrid.innerHTML = '';
        if (products.length === 0) {
            productsGrid.innerHTML = '<p>No products found.</p>';
            return;
        }

        products.forEach(product => {
            const productCard = document.createElement('div');
            productCard.className = 'product-card';
            productCard.innerHTML = `
                <div class="product-image">
                    <img src="${product.images[0]?.url || ''}" alt="${product.title}">
                </div>
                <div class="product-info">
                    <h4>${product.title}</h4>
                    <p>Price: ${product.final_price} ${product.currency}</p>
                    <p>Status: <span class="status-${product.status}">${product.status}</span></p>
                </div>
            `;
            productsGrid.appendChild(productCard);
        });
    };

    const renderPagination = (pagination) => {
        productsPagination.innerHTML = '';
        currentPage = pagination.page;
        totalPages = pagination.total_pages;

        if (totalPages <= 1) return;

        const prevBtn = document.createElement('button');
        prevBtn.textContent = 'Previous';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => fetchProducts(currentPage - 1));
        productsPagination.appendChild(prevBtn);

        const nextBtn = document.createElement('button');
        nextBtn.textContent = 'Next';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => fetchProducts(currentPage + 1));
        productsPagination.appendChild(nextBtn);
    };

    statusFilter.addEventListener('change', () => fetchProducts(1));
    productSearch.addEventListener('input', () => fetchProducts(1));

    // Initial load
    fetchProducts();
});