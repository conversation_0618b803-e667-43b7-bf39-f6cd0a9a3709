"""
File upload routes for JSON data processing
"""
import os
import json
import uuid
from datetime import datetime, timezone
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from services.mongodb_service import mongodb_service
from utils.json_parser import J<PERSON>NParser
import logging

logger = logging.getLogger(__name__)

upload_bp = Blueprint('upload', __name__)

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@upload_bp.route('/json', methods=['POST'])
def upload_json():
    """Upload and process JSON file"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Check file extension
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': 'Invalid file type. Only JSON files are allowed.'
            }), 400
        
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        
        # Save file
        file.save(filepath)
        
        # Get file size
        file_size = os.path.getsize(filepath)
        
        # Create import batch record
        batch_data = {
            'batch_name': f"Import {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            'file_name': filename,
            'file_path': filepath,
            'file_size': file_size,
            'status': 'pending',
            'total_orders': 0,
            'processed_orders': 0,
            'total_products': 0,
            'processed_products': 0,
            'processing_log': []
        }
        
        batch_id = mongodb_service.create_import_batch(batch_data)
        
        if not batch_id:
            return jsonify({
                'success': False,
                'error': 'Failed to create import batch'
            }), 500
        
        # Start processing in background
        import threading
        from utils.background_processor import process_json_file

        # Run in background thread
        thread = threading.Thread(target=process_json_file, args=(batch_id, filepath))
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'data': {
                'batch_id': batch_id,
                'filename': filename,
                'file_size': file_size,
                'status': 'processing'
            }
        })
        
    except Exception as e:
        logger.error(f"Error uploading JSON file: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@upload_bp.route('/status/<batch_id>')
def get_upload_status(batch_id):
    """Get upload processing status"""
    try:
        from bson import ObjectId
        batch = mongodb_service.db.import_batches.find_one({'_id': ObjectId(batch_id)})
        
        if not batch:
            return jsonify({
                'success': False,
                'error': 'Batch not found'
            }), 404
        
        # Convert ObjectId to string
        batch['_id'] = str(batch['_id'])
        
        return jsonify({
            'success': True,
            'data': batch
        })
        
    except Exception as e:
        logger.error(f"Error getting upload status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@upload_bp.route('/validate', methods=['POST'])
def validate_json():
    """Validate JSON file structure without processing"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': 'Invalid file type. Only JSON files are allowed.'
            }), 400
        
        # Read and validate JSON
        try:
            content = file.read()
            data = json.loads(content)
            
            # Basic validation
            if not isinstance(data, list):
                return jsonify({
                    'success': False,
                    'error': 'JSON must contain an array of orders'
                }), 400
            
            if len(data) == 0:
                return jsonify({
                    'success': False,
                    'error': 'JSON file is empty'
                }), 400
            
            # Validate first few records
            parser = JSONParser()
            validation_results = parser.validate_structure(data[:5])
            
            return jsonify({
                'success': True,
                'data': {
                    'total_records': len(data),
                    'validation_results': validation_results,
                    'file_size': len(content),
                    'is_valid': validation_results['is_valid']
                }
            })
            
        except json.JSONDecodeError as e:
            return jsonify({
                'success': False,
                'error': f'Invalid JSON format: {str(e)}'
            }), 400
        
    except Exception as e:
        logger.error(f"Error validating JSON: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@upload_bp.route('/history')
def get_upload_history():
    """Get upload history"""
    try:
        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 20)), 100)
        skip = (page - 1) * limit
        
        # Get import batches
        cursor = mongodb_service.db.import_batches.find().sort('import_date', -1).skip(skip).limit(limit)
        batches = []
        
        for batch in cursor:
            batch['_id'] = str(batch['_id'])
            batches.append(batch)
        
        total = mongodb_service.db.import_batches.count_documents({})
        
        return jsonify({
            'success': True,
            'data': {
                'batches': batches,
                'total': total,
                'page': page,
                'limit': limit,
                'total_pages': (total + limit - 1) // limit
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting upload history: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@upload_bp.route('/retry/<batch_id>', methods=['POST'])
def retry_processing(batch_id):
    """Retry failed processing"""
    try:
        batch = mongodb_service.db.import_batches.find_one({'_id': mongodb_service.ObjectId(batch_id)})
        
        if not batch:
            return jsonify({
                'success': False,
                'error': 'Batch not found'
            }), 404
        
        if batch['status'] not in ['failed', 'completed']:
            return jsonify({
                'success': False,
                'error': 'Can only retry failed or completed batches'
            }), 400
        
        # Reset batch status
        mongodb_service.update_import_batch(batch_id, {
            'status': 'pending',
            'processed_orders': 0,
            'processed_products': 0,
            'processing_log': []
        })
        
        # Start processing again
        from utils.background_processor import process_json_file
        process_json_file.delay(batch_id, batch['file_path'])
        
        return jsonify({
            'success': True,
            'message': 'Processing restarted'
        })
        
    except Exception as e:
        logger.error(f"Error retrying processing: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500