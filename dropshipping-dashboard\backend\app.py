"""
Main Flask Application for Dropshipping Dashboard
"""
import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>
from werkzeug.exceptions import RequestEntityTooLarge

from config import config, Config
from services.mongodb_service import mongodb_service

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize configuration
    config[config_name].init_app(app)
    
    # Enable CORS for frontend communication
    CORS(app)
    
    # Initialize SocketIO for real-time updates
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    # Setup logging
    setup_logging(app)
    
    # Initialize MongoDB connection
    if not mongodb_service.connect():
        app.logger.error("Failed to connect to MongoDB")
        raise Exception("Database connection failed")
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Store socketio instance for use in other modules
    app.socketio = socketio
    
    return app, socketio

def setup_logging(app):
    """Setup application logging"""
    if not app.debug:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = logging.FileHandler('logs/dropshipping_dashboard.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Dropshipping Dashboard startup')

def register_error_handlers(app):
    """Register application error handlers"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({
            'error': 'Not found',
            'message': 'The requested resource was not found'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f'Server Error: {error}')
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }), 500
    
    @app.errorhandler(RequestEntityTooLarge)
    def file_too_large(error):
        return jsonify({
            'error': 'File too large',
            'message': f'File size exceeds maximum limit of {app.config["MAX_CONTENT_LENGTH"]} bytes'
        }), 413
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'Bad request',
            'message': 'The request could not be understood by the server'
        }), 400

def register_blueprints(app):
    """Register application blueprints"""
    
    # Import blueprints
    from routes.api import api_bp
    from routes.products import products_bp
    from routes.orders import orders_bp
    from routes.upload import upload_bp
    from routes.ai_routes import ai_bp
    from routes.image_processing_routes import image_processing_bp
    from routes.workspace import workspace_bp
    from routes.shopify_routes import shopify_bp
    
    # Register blueprints with URL prefixes
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(products_bp, url_prefix='/api/products')
    app.register_blueprint(orders_bp, url_prefix='/api/orders')
    app.register_blueprint(upload_bp, url_prefix='/api/upload')
    app.register_blueprint(ai_bp, url_prefix='/api/ai')
    app.register_blueprint(image_processing_bp, url_prefix='/api/images')
    app.register_blueprint(workspace_bp, url_prefix='/api/workspace')
    app.register_blueprint(shopify_bp, url_prefix='/api/shopify')

# Create the application instance
app, socketio = create_app()

@app.route('/')
def index():
    """Root endpoint"""
    return jsonify({
        'message': 'Dropshipping Dashboard API',
        'version': '1.0.0',
        'status': 'running'
    })

@app.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        stats = mongodb_service.get_dashboard_stats()
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'stats': stats
        })
    except Exception as e:
        app.logger.error(f'Health check failed: {e}')
        return jsonify({
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e)
        }), 500

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    app.logger.info('Client connected')

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    app.logger.info('Client disconnected')

if __name__ == '__main__':
    # Development server
    socketio.run(
        app,
        host='0.0.0.0',
        port=5000,
        debug=True,
        allow_unsafe_werkzeug=True
    )