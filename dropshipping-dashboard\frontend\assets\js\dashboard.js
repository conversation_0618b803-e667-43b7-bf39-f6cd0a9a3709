/* Dashboard JavaScript */

document.addEventListener('DOMContentLoaded', () => {
    const { API_URL, showToast } = window.app;

    // DOM Elements
    const totalProductsEl = document.getElementById('total-products');
    const readyProductsEl = document.getElementById('ready-products');
    const processingProductsEl = document.getElementById('processing-products');
    const totalOrdersEl = document.getElementById('total-orders');
    const refreshDashboardBtn = document.getElementById('refresh-dashboard');

    const fetchDashboardStats = async () => {
        try {
            const response = await fetch(`${API_URL}/dashboard/stats`);
            const result = await response.json();

            if (result.success) {
                const stats = result.data;
                totalProductsEl.textContent = stats.total_products || 0;
                readyProductsEl.textContent = stats.ready_products || 0;
                processingProductsEl.textContent = stats.processing_products || 0;
                totalOrdersEl.textContent = stats.total_orders || 0;
            } else {
                showToast(result.error, 'error');
            }
        } catch (error) {
            showToast('Failed to fetch dashboard stats', 'error');
        }
    };

    refreshDashboardBtn.addEventListener('click', fetchDashboardStats);

    // Make refreshStats available globally
    window.app.refreshStats = fetchDashboardStats;

    // Initial load
    fetchDashboardStats();
});