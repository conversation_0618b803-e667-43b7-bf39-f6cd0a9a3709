#!/usr/bin/env python3

import requests
import json

try:
    # Get workspace products
    response = requests.get('http://localhost:5000/api/workspace/products')
    data = response.json()

    if data['success'] and data['data']['products']:
        product = data['data']['products'][0]
        print(f"Product ID: {product['_id']}")
        print(f"Title: {product['title']}")
        print(f"Images count: {len(product.get('images', []))}")
        print()
        
        for i, img in enumerate(product.get('images', [])):
            print(f"Image {i+1}:")
            print(f"  URL: {img.get('url', 'No URL')}")
            print(f"  Local path: {img.get('local_path', 'No path')}")
            print()
    else:
        print("No products found in workspace")
        
except Exception as e:
    print(f"Error: {e}")
