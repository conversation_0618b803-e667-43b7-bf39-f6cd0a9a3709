# PowerShell script to test image processing
$uri = "http://localhost:5000/api/images/bulk-extract"

# Create request body with product IDs
$body = @{
    product_ids = @(
        "6884d76576dcaf567a7bac2b",
        "6884d76576dcaf567a7bac29"
    )
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Body $body -ContentType "application/json"
    Write-Host "Image processing response:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Image processing failed:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody"
    }
}
